package com.sandu.xinye.common.interceptor;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.VipFeatureRule;
import com.sandu.xinye.common.service.VipFeatureRuleService;
import com.sandu.xinye.common.service.VipPermissionService;
import com.sandu.xinye.common.enums.PermissionType;

import javax.servlet.http.HttpServletRequest;

/**
 * VIP功能权限拦截器
 * 基于数据库规则进行路由匹配和权限校验，避免控制器代码耦合
 */
public class VipFeatureInterceptor implements Interceptor {
    
    @Override
    public void intercept(Invocation inv) {
        try {
            // 获取请求信息
            AppController controller = (AppController) inv.getController();
            HttpServletRequest request = controller.getRequest();
            String method = request.getMethod();
            String path = request.getRequestURI();
            
            // 查找匹配的VIP功能规则
            VipFeatureRule rule = VipFeatureRuleService.me.match(method, path);
            
            // 如果没有匹配的规则，直接放行
            if (rule == null) {
                inv.invoke();
                return;
            }
            
            LogKit.debug("VIP权限检查: " + method + " " + path + " -> " + rule.getPermission());
            
            // 获取当前用户
            User user = controller.getUser();
            if (user == null) {
                // 需要登录但未登录
                controller.renderJson(RetKit.fail("请先登录").set("needLogin", true));
                return;
            }
            
            // 检查VIP权限
            PermissionType permissionType = rule.getPermissionType();
            if (permissionType == null) {
                LogKit.warn("无效的权限类型: " + rule.getPermission());
                inv.invoke();
                return;
            }
            
            boolean hasPermission = VipPermissionService.me.hasPermission(user.getUserId(), permissionType);
            
            if (!hasPermission) {
                // 无VIP权限，返回升级提示
                String message = getUpgradeMessage(permissionType);
                controller.renderJson(
                    RetKit.fail(message)
                        .set("needVip", true)
                        .set("permissionType", permissionType.getCode())
                        .set("upgradeUrl", "/vip/purchase")
                );
                return;
            }
            
            // 权限检查通过，继续执行
            inv.invoke();
            
        } catch (Exception e) {
            LogKit.error("VIP权限拦截器执行失败", e);
            // 出错时放行，避免影响正常功能
            inv.invoke();
        }
    }
    
    /**
     * 获取升级提示信息
     */
    private String getUpgradeMessage(PermissionType permissionType) {
        switch (permissionType) {
            case FEATURE_OCR_CREATE:
                return "识图新建为VIP专享功能，立即开通享受更多高级功能";
            case FEATURE_TEAM_COLLABORATION:
                return "团队协作为VIP专享功能，立即开通享受更多高级功能";
            case EXPERIENCE_AD_FREE:
                return "无广告体验为VIP专享功能，立即开通享受更多高级功能";
            default:
                return "该功能为VIP专享，立即开通享受更多高级功能";
        }
    }
}
