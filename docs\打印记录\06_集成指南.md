# 打印记录功能集成指南

## 1. 概述

打印记录功能用于记录用户的打印行为，包括模板打印、文档打印和图片打印。本文档主要介绍如何在现有的打印流程中集成打印记录功能。

## 2. 后端接口

### 2.1 记录模板打印

**接口地址：** `POST /api/v2/templet/recordPrint`

**请求参数：**
```json
{
  "templateId": 123,        // 模板ID
  "templateName": "商品标签", // 模板名称
  "templateCover": "http://example.com/cover.jpg", // 模板封面
  "printWidth": 50,         // 打印宽度
  "printHeight": 30,        // 打印高度
  "printCopies": 5,         // 打印份数
  "printPlatform": 1,       // 打印端：1-iOS, 2-Android, 3-Windows, 4-Mac
  "templateData": "{...}"   // 模板数据（可选）
}
```

**响应格式：**
```json
{
  "state": "ok",
  "msg": "打印记录保存成功"
}
```

### 2.2 查询打印记录

**接口地址：** `GET /api/v2/printRecord/template/list`

**请求参数：**
```json
{
  "pageNumber": 1,       // 页码，默认1
  "pageSize": 100        // 页大小，默认100
}
```

**响应格式：**
```json
{
  "state": "ok",
  "data": {
    "list": [
      {
        "id": 1,
        "templateId": 123,
        "templateName": "商品标签模板",
        "templateCover": "http://example.com/cover.jpg",
        "printWidth": 50,
        "printHeight": 30,
        "printCopies": 5,
        "printPlatform": 1,
        "printPlatformName": "手机iOS",
        "printTime": "2024-01-15 14:30:00"
      }
    ],
    "totalRow": 150,
    "pageNumber": 1,
    "pageSize": 100,
    "totalPage": 2
  }
}
```

### 2.3 搜索打印记录

**接口地址：** `GET /api/v2/printRecord/template/search`

**请求参数：**
```json
{
  "keyword": "商品",      // 搜索关键词（按名称搜索）
  "width": 50,           // 打印宽度（可选）
  "height": 30,          // 打印高度（可选）
  "pageNumber": 1,       // 页码，默认1
  "pageSize": 20         // 页大小，默认20
}
```

### 2.4 删除打印记录

**接口地址：** `POST /api/v2/printRecord/delete`

**请求参数：**
```json
{
  "id": 123              // 打印记录ID
}
```

## 3. 集成方式

### 3.1 前端集成

在前端打印操作中，需要在执行打印后调用记录接口。以下是集成示例：

```javascript
// 执行打印操作
function printTemplate(template) {
  // 执行实际的打印操作
  // ...

  // 记录打印行为
  recordPrint(template);
}

// 记录打印行为
function recordPrint(template) {
  const data = {
    templateId: template.id,
    templateName: template.name,
    templateCover: template.cover,
    printWidth: template.width,
    printHeight: template.height,
    printCopies: getPrintCopies(), // 获取打印份数
    printPlatform: detectPlatform(), // 检测打印平台
    templateData: template.data
  };

  // 调用记录接口
  fetch('/api/v2/templet/recordPrint', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(result => {
    console.log('打印记录保存结果:', result);
  })
  .catch(error => {
    console.error('打印记录保存失败:', error);
  });
}

// 检测打印平台
function detectPlatform() {
  const userAgent = navigator.userAgent.toLowerCase();
  if (/iphone|ipad|ipod/.test(userAgent)) {
    return 1; // iOS
  } else if (/android/.test(userAgent)) {
    return 2; // Android
  } else if (/macintosh|mac os x/.test(userAgent)) {
    return 4; // Mac
  } else if (/windows|win32|win64/.test(userAgent)) {
    return 3; // Windows
  }
  return 3; // 默认Windows
}
```

### 3.2 后端集成

如果打印操作是在后端执行的，可以使用提供的工具类来记录打印行为：

```java
// 方式1：使用PrintRecordKit工具类
PrintRecordKit.saveTemplatePrintRecord(userId, templet, printCopies, printPlatform);

// 方式2：异步记录（推荐）
PrintRecordKit.saveTemplatePrintRecordAsync(userId, templet, printCopies, printPlatform);

// 方式3：通过Service记录
PrintRecordKit.saveTemplatePrintRecordViaService(userId, templateId, templateName, 
        templateCover, printWidth, printHeight, printCopies, printPlatform, templateData);
```

## 4. 注意事项

1. 打印记录功能不会影响现有的打印流程，只是在打印完成后记录打印行为
2. 打印记录采用软删除机制，删除操作不会物理删除数据
3. 打印端类型可以自动检测，也可以手动指定
4. 打印记录默认按时间倒序排列
5. 一期只实现模板打印记录功能，文档打印和图片打印记录功能后续实现

## 5. 测试方法

可以使用以下方法测试打印记录功能：

1. 使用Postman等工具调用记录接口
2. 在前端打印操作中集成记录功能
3. 查询打印记录列表验证记录是否保存成功
4. 测试搜索和删除功能
