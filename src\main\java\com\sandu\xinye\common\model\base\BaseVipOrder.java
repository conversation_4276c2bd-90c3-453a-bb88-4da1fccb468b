package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseVipOrder<M extends BaseVipOrder<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setOrderNo(java.lang.String orderNo) {
		set("order_no", orderNo);
		return (M)this;
	}
	
	public java.lang.String getOrderNo() {
		return getStr("order_no");
	}

	public M setUserId(java.lang.Integer userId) {
		set("user_id", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("user_id");
	}

	public M setPlan(java.lang.String plan) {
		set("plan", plan);
		return (M)this;
	}
	
	public java.lang.String getPlan() {
		return getStr("plan");
	}

	public M setTier(java.lang.String tier) {
		set("tier", tier);
		return (M)this;
	}
	
	public java.lang.String getTier() {
		return getStr("tier");
	}

	public M setAmount(java.lang.Integer amount) {
		set("amount", amount);
		return (M)this;
	}
	
	public java.lang.Integer getAmount() {
		return getInt("amount");
	}

	public M setChannel(java.lang.String channel) {
		set("channel", channel);
		return (M)this;
	}
	
	public java.lang.String getChannel() {
		return getStr("channel");
	}

	public M setStatus(java.lang.String status) {
		set("status", status);
		return (M)this;
	}
	
	public java.lang.String getStatus() {
		return getStr("status");
	}

	public M setChannelTradeNo(java.lang.String channelTradeNo) {
		set("channel_trade_no", channelTradeNo);
		return (M)this;
	}
	
	public java.lang.String getChannelTradeNo() {
		return getStr("channel_trade_no");
	}

	public M setPaidTime(java.util.Date paidTime) {
		set("paid_time", paidTime);
		return (M)this;
	}
	
	public java.util.Date getPaidTime() {
		return get("paid_time");
	}

	public M setNotifyTime(java.util.Date notifyTime) {
		set("notify_time", notifyTime);
		return (M)this;
	}
	
	public java.util.Date getNotifyTime() {
		return get("notify_time");
	}

	public M setExtra(java.lang.String extra) {
		set("extra", extra);
		return (M)this;
	}
	
	public java.lang.String getExtra() {
		return getStr("extra");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public M setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
		return (M)this;
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
