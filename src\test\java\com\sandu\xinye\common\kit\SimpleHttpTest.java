package com.sandu.xinye.common.kit;

import com.alibaba.fastjson.JSON;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 简单的HTTP测试，验证代理商接口的连通性
 */
public class SimpleHttpTest {
    
    @Test
    public void testDirectApiCall() {
        System.out.println("=== 直接测试代理商API ===");
        
        String url = "https://support.xpyun.net/api/admin/member/verifyPromotion";
        
        // 先测试GET请求看接口是否存在
        testGetRequest(url);
        
        // 再测试POST请求
        testPostRequest(url);
    }
    
    private void testGetRequest(String url) {
        System.out.println("\n--- GET请求测试 ---");
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        
        try {
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url);
            
            response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            HttpEntity responseEntity = response.getEntity();
            
            System.out.println("状态码: " + statusCode);
            if (responseEntity != null) {
                String responseContent = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                System.out.println("响应内容: " + responseContent);
            }
            
        } catch (Exception e) {
            System.out.println("GET请求异常: " + e.getMessage());
        } finally {
            try {
                if (response != null) response.close();
                if (httpClient != null) httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    
    private void testPostRequest(String url) {
        System.out.println("\n--- POST请求测试 ---");
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        
        try {
            httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            
            // 构建测试参数
            long timestamp = System.currentTimeMillis();
            String user = "12345";
            String sign = com.jfinal.kit.HashKit.sha1(user + "123!@#wqee" + timestamp);
            
            Map<String, Object> params = new HashMap<>();
            params.put("user", user);
            params.put("timestamp", String.valueOf(timestamp));
            params.put("sign", sign);
            params.put("promotionCode", "RIPQFW");
            
            String jsonParams = JSON.toJSONString(params);
            StringEntity entity = new StringEntity(jsonParams, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            
            System.out.println("请求参数: " + jsonParams);
            
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            HttpEntity responseEntity = response.getEntity();
            
            System.out.println("状态码: " + statusCode);
            if (responseEntity != null) {
                String responseContent = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                System.out.println("响应内容: " + responseContent);
            }
            
        } catch (Exception e) {
            System.out.println("POST请求异常: " + e.getMessage());
        } finally {
            try {
                if (response != null) response.close();
                if (httpClient != null) httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    
    @Test 
    public void testDomainConnectivity() {
        System.out.println("=== 测试域名连通性 ===");
        
        String baseUrl = "https://support.xpyun.net";
        
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        
        try {
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(baseUrl);
            
            response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            
            System.out.println("域名: " + baseUrl);
            System.out.println("状态码: " + statusCode);
            System.out.println("域名可访问性: " + (statusCode < 500 ? "可访问" : "不可访问"));
            
        } catch (Exception e) {
            System.out.println("域名连接异常: " + e.getMessage());
        } finally {
            try {
                if (response != null) response.close();
                if (httpClient != null) httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}