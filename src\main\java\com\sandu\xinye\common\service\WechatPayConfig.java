package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;

/**
 * 微信支付配置管理
 */
public class WechatPayConfig {
    
    public static final WechatPayConfig me = new WechatPayConfig();
    
    // 配置键名
    private static final String WECHAT_PAY_APPID = "wechat.pay.appid";
    private static final String WECHAT_PAY_MCHID = "wechat.pay.mchid";
    private static final String WECHAT_PAY_API_V3_KEY = "wechat.pay.api.v3.key";
    private static final String WECHAT_PAY_SERIAL_NO = "wechat.pay.serial.no";
    private static final String WECHAT_PAY_PRIVATE_KEY_PATH = "wechat.pay.private.key.path";
    private static final String WECHAT_PAY_NOTIFY_URL = "wechat.pay.notify.url";
    
    // 微信支付API地址
    private static final String WECHAT_PAY_API_BASE_URL = "https://api.mch.weixin.qq.com";
    private static final String WECHAT_PAY_APP_ORDER_URL = "/v3/pay/transactions/app";
    
    /**
     * 获取应用ID
     */
    public String getAppId() {
        return PropKit.get(WECHAT_PAY_APPID, "");
    }
    
    /**
     * 获取商户号
     */
    public String getMchId() {
        return PropKit.get(WECHAT_PAY_MCHID, "");
    }
    
    /**
     * 获取API v3密钥
     */
    public String getApiV3Key() {
        return PropKit.get(WECHAT_PAY_API_V3_KEY, "");
    }
    
    /**
     * 获取证书序列号
     */
    public String getSerialNo() {
        return PropKit.get(WECHAT_PAY_SERIAL_NO, "");
    }
    
    /**
     * 获取私钥文件路径
     */
    public String getPrivateKeyPath() {
        return PropKit.get(WECHAT_PAY_PRIVATE_KEY_PATH, "");
    }
    
    /**
     * 获取支付回调地址
     */
    public String getNotifyUrl() {
        return PropKit.get(WECHAT_PAY_NOTIFY_URL, "");
    }
    
    /**
     * 获取APP下单API地址
     */
    public String getAppOrderUrl() {
        return WECHAT_PAY_API_BASE_URL + WECHAT_PAY_APP_ORDER_URL;
    }
    
    /**
     * 检查配置是否完整
     */
    public boolean isConfigValid() {
        boolean valid = true;
        
        if (getAppId().isEmpty()) {
            LogKit.warn("微信支付配置缺失: " + WECHAT_PAY_APPID);
            valid = false;
        }
        
        if (getMchId().isEmpty()) {
            LogKit.warn("微信支付配置缺失: " + WECHAT_PAY_MCHID);
            valid = false;
        }
        
        if (getApiV3Key().isEmpty()) {
            LogKit.warn("微信支付配置缺失: " + WECHAT_PAY_API_V3_KEY);
            valid = false;
        }
        
        if (getSerialNo().isEmpty()) {
            LogKit.warn("微信支付配置缺失: " + WECHAT_PAY_SERIAL_NO);
            valid = false;
        }
        
        if (getPrivateKeyPath().isEmpty()) {
            LogKit.warn("微信支付配置缺失: " + WECHAT_PAY_PRIVATE_KEY_PATH);
            valid = false;
        }
        
        if (getNotifyUrl().isEmpty()) {
            LogKit.warn("微信支付配置缺失: " + WECHAT_PAY_NOTIFY_URL);
            valid = false;
        }
        
        return valid;
    }
    
    /**
     * 打印配置状态（隐藏敏感信息）
     */
    public void printConfigStatus() {
        LogKit.info("微信支付配置状态:");
        LogKit.info("  AppId: " + maskSensitive(getAppId()));
        LogKit.info("  MchId: " + maskSensitive(getMchId()));
        LogKit.info("  ApiV3Key: " + (getApiV3Key().isEmpty() ? "未配置" : "已配置"));
        LogKit.info("  SerialNo: " + maskSensitive(getSerialNo()));
        LogKit.info("  PrivateKeyPath: " + getPrivateKeyPath());
        LogKit.info("  NotifyUrl: " + getNotifyUrl());
        LogKit.info("  配置完整性: " + (isConfigValid() ? "完整" : "不完整"));
    }
    
    /**
     * 隐藏敏感信息
     */
    private String maskSensitive(String value) {
        if (value == null || value.length() <= 4) {
            return value;
        }
        return value.substring(0, 4) + "****" + value.substring(value.length() - 4);
    }
}
