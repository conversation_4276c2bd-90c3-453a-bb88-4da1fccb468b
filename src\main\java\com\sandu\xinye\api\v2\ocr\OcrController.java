package com.sandu.xinye.api.v2.ocr;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AppUserInterceptor;
import com.sandu.xinye.common.kit.RetKit;

/**
 * OCR识图控制器
 * 提供图片识别功能，将上传的图片通过TextIn API进行识别，
 * 转换为APP端可用的JSON格式数据
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class OcrController extends AppController {

    /**
     * 图片识别接口
     * POST /api/v2/ocr/recognize
     * 
     * 请求参数:
     * - file: 图片文件 (multipart/form-data)
     * - imageWidth: 图片宽度(px) - 可选
     * - imageHeight: 图片高度(px) - 可选
     */
    @Before({AppUserInterceptor.class})
    public void recognize() {
        LogKit.info("OCR识图请求开始 - 用户ID: " + getUser().getUserId());
        
        try {
            // 获取上传的图片文件
            UploadFile imageFile = getFile("file");
            if (imageFile == null) {
                renderJson(RetKit.fail("请上传图片文件"));
                return;
            }
            
            // 获取可选参数
            Integer imageWidth = getParaToInt("imageWidth");
            Integer imageHeight = getParaToInt("imageHeight");
            
            LogKit.info("接收到图片文件: " + imageFile.getFileName() + 
                       ", 大小: " + imageFile.getFile().length() + " bytes");
            
            // 调用服务层处理
            RetKit result = OcrService.me.recognizeImage(imageFile, imageWidth, imageHeight);
            
            renderJson(result);
            
        } catch (Exception e) {
            LogKit.error("OCR识图处理异常: " + e.getMessage(), e);
            renderJson(RetKit.fail("图片识别失败: " + e.getMessage()));
        }
        
        LogKit.info("OCR识图请求结束");
    }
    
    /**
     * 健康检查接口
     * GET /api/v2/ocr/health
     */
    public void health() {
        renderJson(RetKit.ok("OCR服务运行正常"));
    }
}
