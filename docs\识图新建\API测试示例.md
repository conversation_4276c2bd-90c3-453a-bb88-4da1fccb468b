# 识图新建API测试示例

## 接口信息
- **URL**: `POST /api/v2/ocr/recognize`
- **Content-Type**: `multipart/form-data`
- **认证**: 需要用户登录（AppUserInterceptor）
- **第三方服务**: TextIn文档解析API

## 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 图片文件，支持jpg、png、webp等格式 |
| imageWidth | Integer | 否 | 图片宽度(px)，不传则从文件中获取 |
| imageHeight | Integer | 否 | 图片高度(px)，不传则从文件中获取 |

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    "imageInfo": {
      "width": 1080,
      "height": 1920,
      "format": "jpeg"
    },
    "elements": [
      {
        "elementType": "1",
        "x": 100,
        "y": 200,
        "width": 300,
        "height": 50,
        "content": "产品标题",
        "charWidth": 25.0,
        "bold": true,
        "italic": false,
        "rotationAngle": 0.0,
        "textDirection": "horizontal",
        "confidence": 0.99,
        "isHandwritten": false
      },
      {
        "elementType": "1",
        "x": 100,
        "y": 260,
        "width": 280,
        "height": 40,
        "content": "倾斜文字内容",
        "charWidth": 23.3,
        "bold": false,
        "italic": true,
        "rotationAngle": 15.5,
        "textDirection": "horizontal",
        "confidence": 0.95,
        "isHandwritten": false
      },
      {
        "elementType": "2",
        "x": 50,
        "y": 300,
        "width": 400,
        "height": 80,
        "content": "6975370866829",
        "barcodeType": "CODE_128"
      },
      {
        "elementType": "7",
        "x": 200,
        "y": 400,
        "width": 100,
        "height": 100,
        "content": "https://example.com"
      },
      {
        "elementType": "8",
        "x": 450,
        "y": 50,
        "width": 80,
        "height": 60,
        "imageUrl": "https://web-api.textin.com/ocr_image/external/logo_123.png",
        "imageType": "logo",
        "confidence": 0.92,
        "isEmbedded": true
      },
      {
        "elementType": "10",
        "x": 50,
        "y": 550,
        "width": 300,
        "height": 150,
        "rowCount": 3,
        "colCount": 2,
        "cells": [
          {
            "row": 0,
            "col": 0,
            "content": "商品名称"
          },
          {
            "row": 0,
            "col": 1,
            "content": "价格"
          },
          {
            "row": 1,
            "col": 0,
            "content": "苹果"
          },
          {
            "row": 1,
            "col": 1,
            "content": "￥5.00"
          },
          {
            "row": 2,
            "col": 0,
            "content": "香蕉",
            "rowSpan": 1,
            "colSpan": 2
          }
        ]
      },
      {
        "elementType": "11",
        "x": 400,
        "y": 550,
        "width": 200,
        "height": 100
      }
    ]
  },
  "message": "识别成功"
}
```

### 错误响应
```json
{
  "success": false,
  "msg": "图片文件过大，最大支持10MB"
}
```

## 元素类型说明

### 1. 文本元素 (elementType: "1")
- **content**: 识别到的文本内容
- **charWidth**: 智能计算的平均字符宽度（像素）
- **bold**: 是否粗体（基于TextIn的sub_type和outline_level智能判断）
- **italic**: 是否斜体（基于char_positions几何分析智能判断）
- **rotationAngle**: 旋转角度（度，来自TextIn的angle字段）
- **textDirection**: 文字方向（"horizontal"/"vertical"，基于direction字段）
- **confidence**: 识别置信度（0-1，来自TextIn的score字段）
- **isHandwritten**: 是否手写体（布尔值，来自handwritten字段）
- **x/y/width/height**: 位置和尺寸（像素单位）

### 2. 条形码元素 (elementType: "2")
- **content**: 条码内容
- **barcodeType**: 标准条码类型名称（CODE_39, CODE_128, EAN_13等）
- **x/y/width/height**: 位置和尺寸（像素单位）

### 3. 二维码元素 (elementType: "7")
- **content**: 二维码内容
- **x/y/width/height**: 位置和尺寸（像素单位）

### 4. 图片元素 (elementType: "8") 🆕
- **imageUrl**: 图片链接或base64数据（来自TextIn返回的图片资源地址）
- **imageType**: 图片子类型（logo、stamp、chart、icon、graphic、generic）
- **confidence**: 识别置信度（0-1，表示识别结果的可信程度）
- **isEmbedded**: 是否为嵌入式图片（布尔值，true表示图片数据直接包含在响应中）
- **x/y/width/height**: 位置和尺寸（像素单位，通过bbox计算得出）

### 5. 表格元素 (elementType: "10")
- **rowCount/colCount**: 基于TextIn cells数据智能计算的实际行列数
- **cells**: 完整的单元格数组，包含：
  - **row/col**: 单元格行列位置
  - **content**: 单元格内容（来自TextIn的text字段）
  - **rowSpan/colSpan**: 跨行跨列信息（如果存在）
- **x/y/width/height**: 位置和尺寸（像素单位）

### 6. 边框元素 (elementType: "11") 🆕
- **说明**: 识别为边框的元素，通常是1行1列且内容为空的表格结构
- **特征**: 只包含位置尺寸信息，没有内容数据
- **用途**: 用于识别装饰性边框、空白表格框等元素
- **x/y/width/height**: 位置和尺寸（像素单位）

## 测试用例

### 0. 边框元素测试专项 🆕

#### 测试图片推荐
建议使用包含以下元素的图片进行测试：
- **跨境物流标签**: 如 `data/跨境物流.png`，包含多个边框元素
- **包装盒模板**: 含多个空白装饰框的设计稿
- **表格模板**: 包含空表格框的文档模板

#### 边框元素验证清单
测试时重点验证以下内容：

1. **elementType正确性**：确认边框元素返回 `"elementType": "11"`
2. **位置尺寸准确性**：验证x、y、width、height是否与实际图片中的边框位置匹配
3. **识别精确性**：只有1行1列且内容为空的表格才应该被识别为边框
4. **与表格区分**：有内容的表格应该正确识别为表格元素（elementType: "10"）

#### 预期结果示例
对于跨境物流图片，应识别出：
- 文本元素：各种标签文字
- 二维码元素：二维码内容
- 边框元素：多个空白装饰框

```json
{
  "elementType": "11",
  "x": 50,
  "y": 300,
  "width": 200,
  "height": 100
}
```

### 1. 图片元素测试专项

#### 测试图片推荐
建议使用包含以下元素的图片进行测试：
- **设备标识标签**: 如 `data/设备标识3（40_30）.png`，包含中国铁塔logo
- **商品包装**: 含品牌标识和二维码的商品标签
- **证件文档**: 含印章和logo的正式文档

#### 图片元素验证清单
测试时重点验证以下内容：

1. **elementType正确性**：确认图片元素返回 `"elementType": "8"`
2. **位置尺寸准确性**：验证x、y、width、height是否与实际图片中的位置匹配
3. **图片类型分类**：检查imageType是否正确分类（logo/stamp/chart/icon等）
4. **图片URL有效性**：验证imageUrl是否为有效的链接或base64数据
5. **置信度合理性**：确认confidence在0.8-1.0的合理范围内

#### 预期结果示例
对于设备标识3图片，应识别出：
- 文本元素：站点名称、器件名称、施工单位等
- 图片元素：右上角的中国铁塔logo（三角形标识）

```json
{
  "elementType": "8",
  "x": 485,
  "y": 25,
  "width": 60,
  "height": 75,
  "imageUrl": "https://web-api.textin.com/ocr_image/external/china_tower_logo_xxx.jpg",
  "imageType": "logo",
  "confidence": 0.91,
  "isEmbedded": true
}
```

### 2. 使用curl测试
```bash
curl -X POST \
  http://localhost:8090/api/v2/ocr/recognize \
  -H 'Content-Type: multipart/form-data' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -F 'file=@test_image.jpg' \
  -F 'imageWidth=800' \
  -F 'imageHeight=600'
```

### 3. 使用Postman测试
1. 设置请求方法为POST
2. URL: `http://localhost:8090/api/v2/ocr/recognize`
3. Headers添加认证信息
4. Body选择form-data
5. 添加file字段，选择图片文件
6. 可选添加imageWidth和imageHeight参数

### 4. JavaScript测试示例
```javascript
const formData = new FormData();
formData.append('file', imageFile);
formData.append('imageWidth', '800');
formData.append('imageHeight', '600');

fetch('/api/v2/ocr/recognize', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('识别成功:', data.data);
    // 处理识别结果
    data.data.elements.forEach(element => {
      console.log('元素类型:', element.elementType);
      console.log('内容:', element.content);
      console.log('位置:', element.x, element.y);
    });
  } else {
    console.error('识别失败:', data.msg);
  }
})
.catch(error => {
  console.error('请求失败:', error);
});
```

## 错误码说明
| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| 请上传图片文件 | 未上传文件或文件为空 | 检查文件上传 |
| 图片文件过大，最大支持10MB | 文件超过大小限制 | 压缩图片或选择更小的文件 |
| 不支持的图片格式 | 文件格式不支持 | 使用jpg、png、webp等格式 |
| 图片文件为空 | 文件大小为0 | 检查文件是否损坏 |
| TextIn API调用失败 | 第三方服务异常 | 检查网络连接和API配置 |

## 性能指标
- **响应时间**: 通常5-15秒（取决于图片大小和复杂度）
- **文件大小限制**: 最大10MB
- **支持格式**: jpg、jpeg、png、webp、bmp
- **并发限制**: 建议不超过10个并发请求

## 注意事项
1. 需要配置TextIn API的密钥和应用ID
2. 图片质量影响识别准确率，建议使用清晰、光线充足的图片
3. 复杂表格的识别可能需要人工校验
4. 条码识别支持常见的一维码和二维码格式
5. **坐标系统使用像素单位**，返回简化的中间态数据结构
6. 数值类型字段（坐标、尺寸）为数字类型，布尔字段为布尔类型
7. 条码类型使用标准字符串名称，便于APP端处理
8. **字体样式智能判断**：
   - bold基于TextIn的sub_type（如"text_title"）和outline_level智能判断
   - italic基于char_positions四角坐标几何分析，计算字符倾斜角度判断（阈值12度）
   - charWidth采用字符分类算法，优先使用中文字符宽度作为基准
9. **表格结构完整解析**：
   - 充分利用TextIn的cells数组，包含每个单元格的精确位置和内容
   - 支持跨行跨列单元格（rowSpan/colSpan）
   - 智能计算表格的实际行列数
10. **文本详细属性**：
    - rotationAngle: 从TextIn的angle字段提取文字旋转角度
    - textDirection: 基于direction字段判断文字排列方向
    - confidence: 传递TextIn的识别置信度给APP端
    - isHandwritten: 正确区分手写体和斜体概念
11. **边框与表格区分** 🆕：
    - 1行1列且内容为空的表格自动识别为边框元素（elementType: "11"）
    - 有内容或多行多列的表格正确识别为表格元素（elementType: "10"）
    - 边框元素不包含cells数组，只有位置尺寸信息
12. **条码识别优化** 🆕：
    - ZXing只补充TextIn检测到但内容为空的条码
    - 优先使用TextIn提供的条码图片URL进行识别，准确率更高
    - 支持多种二值化策略和识别参数优化
13. **文本格式清理** 🆕：
    - 自动清理markdown格式符号（**粗体**、*斜体*等）
    - 移除HTML img标签，避免影响文本内容显示

## 配置要求
在`common_config.txt`中添加以下配置：
```
# TextIn文档解析API配置
textin.api.url=https://api.textin.com/ai/service/v1/pdf_to_markdown
textin.api.key=your_actual_api_key
textin.app.id=your_actual_app_id
```

## 部署前检查
1. **依赖包检查**: 确保pom.xml中已添加必要的依赖
2. **配置验证**: 检查TextIn API密钥和应用ID是否正确
3. **网络连接**: 确保服务器可以访问TextIn API
4. **路由注册**: 确认ApiRoutes中已添加OCR路由

## 运行测试
```bash
# 编译项目
mvn compile

# 运行单元测试
mvn test -Dtest=OcrServiceTest
mvn test -Dtest=CoordinateUtilsTest
mvn test -Dtest=ElementConverterTest
mvn test -Dtest=ImageElementTest        # 图片元素测试 🆕

# 启动服务
mvn jetty:run
```
