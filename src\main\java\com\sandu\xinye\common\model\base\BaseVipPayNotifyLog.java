package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseVipPayNotifyLog<M extends BaseVipPayNotifyLog<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setChannel(java.lang.String channel) {
		set("channel", channel);
		return (M)this;
	}
	
	public java.lang.String getChannel() {
		return getStr("channel");
	}

	public M setOrderNo(java.lang.String orderNo) {
		set("order_no", orderNo);
		return (M)this;
	}
	
	public java.lang.String getOrderNo() {
		return getStr("order_no");
	}

	public M setChannelTradeNo(java.lang.String channelTradeNo) {
		set("channel_trade_no", channelTradeNo);
		return (M)this;
	}
	
	public java.lang.String getChannelTradeNo() {
		return getStr("channel_trade_no");
	}

	public M setNotifyTime(java.util.Date notifyTime) {
		set("notify_time", notifyTime);
		return (M)this;
	}
	
	public java.util.Date getNotifyTime() {
		return get("notify_time");
	}

	public M setRawHeaders(java.lang.String rawHeaders) {
		set("raw_headers", rawHeaders);
		return (M)this;
	}
	
	public java.lang.String getRawHeaders() {
		return getStr("raw_headers");
	}

	public M setRawBody(java.lang.String rawBody) {
		set("raw_body", rawBody);
		return (M)this;
	}
	
	public java.lang.String getRawBody() {
		return getStr("raw_body");
	}

	public M setVerifyResult(java.lang.String verifyResult) {
		set("verify_result", verifyResult);
		return (M)this;
	}
	
	public java.lang.String getVerifyResult() {
		return getStr("verify_result");
	}

	public M setHandleStatus(java.lang.String handleStatus) {
		set("handle_status", handleStatus);
		return (M)this;
	}
	
	public java.lang.String getHandleStatus() {
		return getStr("handle_status");
	}

	public M setErrorMsg(java.lang.String errorMsg) {
		set("error_msg", errorMsg);
		return (M)this;
	}
	
	public java.lang.String getErrorMsg() {
		return getStr("error_msg");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
