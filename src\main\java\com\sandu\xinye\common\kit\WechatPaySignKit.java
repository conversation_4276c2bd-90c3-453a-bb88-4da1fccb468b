package com.sandu.xinye.common.kit;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

/**
 * 微信支付签名工具类
 * 支持微信支付 API v3 的签名生成和验证
 */
public class WechatPaySignKit {
    
    private static final String ALGORITHM = "SHA256withRSA";
    private static final String AES_ALGORITHM = "AES/GCM/NoPadding";
    
    /**
     * 生成微信支付签名
     */
    public static String sign(String method, String url, String timestamp, String nonce, String body, RSAPrivateKey privateKey) {
        try {
            // 构建签名串
            String signatureStr = buildSignatureString(method, url, timestamp, nonce, body);
            
            // 使用私钥签名
            Signature signature = Signature.getInstance(ALGORITHM);
            signature.initSign(privateKey);
            signature.update(signatureStr.getBytes(StandardCharsets.UTF_8));
            
            byte[] signBytes = signature.sign();
            return Base64.getEncoder().encodeToString(signBytes);
            
        } catch (Exception e) {
            LogKit.error("微信支付签名失败", e);
            throw new RuntimeException("微信支付签名失败", e);
        }
    }
    
    /**
     * 验证微信支付回调签名
     */
    public static boolean verifySignature(String timestamp, String nonce, String body, String signature, X509Certificate certificate) {
        try {
            // 构建签名串
            String signatureStr = buildCallbackSignatureString(timestamp, nonce, body);
            
            // 使用证书公钥验证签名
            Signature verifier = Signature.getInstance(ALGORITHM);
            verifier.initVerify(certificate.getPublicKey());
            verifier.update(signatureStr.getBytes(StandardCharsets.UTF_8));
            
            byte[] signBytes = Base64.getDecoder().decode(signature);
            return verifier.verify(signBytes);
            
        } catch (Exception e) {
            LogKit.error("微信支付签名验证失败", e);
            return false;
        }
    }
    
    /**
     * 解密微信支付回调数据
     */
    public static String decryptCallbackData(String associatedData, String nonce, String ciphertext, String apiV3Key) {
        try {
            byte[] key = apiV3Key.getBytes(StandardCharsets.UTF_8);
            byte[] nonceBytes = nonce.getBytes(StandardCharsets.UTF_8);
            byte[] associatedDataBytes = associatedData.getBytes(StandardCharsets.UTF_8);
            byte[] ciphertextBytes = Base64.getDecoder().decode(ciphertext);
            
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            GCMParameterSpec spec = new GCMParameterSpec(128, nonceBytes);
            SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
            
            cipher.init(Cipher.DECRYPT_MODE, secretKey, spec);
            cipher.updateAAD(associatedDataBytes);
            
            byte[] decryptedBytes = cipher.doFinal(ciphertextBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            LogKit.error("微信支付回调数据解密失败", e);
            throw new RuntimeException("微信支付回调数据解密失败", e);
        }
    }
    
    /**
     * 从文件加载RSA私钥
     */
    public static RSAPrivateKey loadPrivateKey(String privateKeyPath) {
        try {
            String privateKeyContent = new String(Files.readAllBytes(Paths.get(privateKeyPath)), StandardCharsets.UTF_8);
            
            // 移除PEM格式的头尾标记
            privateKeyContent = privateKeyContent
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
            
            byte[] keyBytes = Base64.getDecoder().decode(privateKeyContent);
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            
            return (RSAPrivateKey) keyFactory.generatePrivate(spec);
            
        } catch (Exception e) {
            LogKit.error("加载微信支付私钥失败: " + privateKeyPath, e);
            throw new RuntimeException("加载微信支付私钥失败", e);
        }
    }
    
    /**
     * 从字符串加载X509证书
     */
    public static X509Certificate loadCertificate(String certificateContent) {
        try {
            CertificateFactory factory = CertificateFactory.getInstance("X.509");
            ByteArrayInputStream inputStream = new ByteArrayInputStream(certificateContent.getBytes(StandardCharsets.UTF_8));
            return (X509Certificate) factory.generateCertificate(inputStream);
            
        } catch (Exception e) {
            LogKit.error("加载微信支付证书失败", e);
            throw new RuntimeException("加载微信支付证书失败", e);
        }
    }
    
    /**
     * 构建签名串（请求签名）
     */
    private static String buildSignatureString(String method, String url, String timestamp, String nonce, String body) {
        return method + "\n" + url + "\n" + timestamp + "\n" + nonce + "\n" + (body == null ? "" : body) + "\n";
    }
    
    /**
     * 构建签名串（回调验证）
     */
    private static String buildCallbackSignatureString(String timestamp, String nonce, String body) {
        return timestamp + "\n" + nonce + "\n" + (body == null ? "" : body) + "\n";
    }
    
    /**
     * 生成随机字符串
     */
    public static String generateNonce() {
        return RandomKit.getRandomCharAndNum(32);
    }
    
    /**
     * 生成当前时间戳
     */
    public static String generateTimestamp() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }
}
