# XPrinter 打印记录 API 接口文档

## 1. 接口概述

XPrinter打印记录功能提供了完整的打印行为记录和查询功能，支持模板打印记录的增删查改操作。

### 1.1 基础信息

- **API版本**: v2
- **基础URL**: `/api/v2`
- **认证方式**: 用户登录认证
- **数据格式**: JSON

### 1.2 通用响应格式

```json
{
  "state": "ok|fail",
  "msg": "响应消息",
  "data": {}
}
```

## 2. 模板打印记录接口

### 2.1 获取模板打印记录列表

**接口地址**: `GET /api/v2/printRecord/getTemplateList`

**功能描述**: 获取用户的模板打印记录列表，支持分页查询

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| pageNumber | Integer | 否 | 1 | 页码 |
| pageSize | Integer | 否 | 100 | 页大小 |

**请求示例**:
```
GET /api/v2/printRecord/getTemplateList?pageNumber=1&pageSize=100
```

**响应示例**:
```json
{
  "state": "ok",
  "data": {
    "list": [
      {
        "id": 1,
        "templateId": 123,
        "templateName": "商品标签模板",
        "templateCover": "http://example.com/cover.jpg",
        "printWidth": 50,
        "printHeight": 30,
        "printSizeString": "50x30",
        "printCopies": 5,
        "printPlatform": 1,
        "printPlatformName": "手机iOS",
        "printTime": "2024-01-15 14:30:00",
        "printStatus": 1,
        "printStatusName": "成功"
      }
    ],
    "totalRow": 150,
    "pageNumber": 1,
    "pageSize": 100,
    "totalPage": 2
  }
}
```

### 2.2 搜索模板打印记录

**接口地址**: `GET /api/v2/printRecord/searchTemplate`

**功能描述**: 按名称、尺寸搜索模板打印记录

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| keyword | String | 否 | - | 搜索关键词（按模板名称搜索） |
| width | Integer | 否 | - | 打印宽度 |
| height | Integer | 否 | - | 打印高度 |
| pageNumber | Integer | 否 | 1 | 页码 |
| pageSize | Integer | 否 | 20 | 页大小 |

**请求示例**:
```
GET /api/v2/printRecord/searchTemplate?keyword=商品&width=50&height=30&pageNumber=1&pageSize=20
```

**响应示例**:
```json
{
  "state": "ok",
  "data": {
    "list": [
      {
        "id": 1,
        "templateId": 123,
        "templateName": "商品标签模板",
        "templateCover": "http://example.com/cover.jpg",
        "printWidth": 50,
        "printHeight": 30,
        "printSizeString": "50x30",
        "printCopies": 5,
        "printPlatform": 1,
        "printPlatformName": "手机iOS",
        "printTime": "2024-01-15 14:30:00",
        "printStatus": 1,
        "printStatusName": "成功"
      }
    ],
    "totalRow": 10,
    "pageNumber": 1,
    "pageSize": 20,
    "totalPage": 1
  }
}
```

### 2.3 删除打印记录

**接口地址**: `POST /api/v2/printRecord/delete`

**功能描述**: 软删除指定的打印记录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 打印记录ID |

**请求示例**:
```json
{
  "id": 123
}
```

**响应示例**:
```json
{
  "state": "ok",
  "msg": "删除成功"
}
```

### 2.4 获取打印记录详情

**接口地址**: `GET /api/v2/printRecord/detail/{id}`

**功能描述**: 获取单条打印记录的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 打印记录ID |

**请求示例**:
```
GET /api/v2/printRecord/detail/123
```

**响应示例**:
```json
{
  "state": "ok",
  "data": {
    "id": 1,
    "templateId": 123,
    "templateName": "商品标签模板",
    "templateCover": "http://example.com/cover.jpg",
    "printWidth": 50,
    "printHeight": 30,
    "printSizeString": "50x30",
    "printCopies": 5,
    "printPlatform": 1,
    "printPlatformName": "手机iOS",
    "printTime": "2024-01-15 14:30:00",
    "sourceData": "{...}",
    "printStatus": 1,
    "printStatusName": "成功",
    "createTime": "2024-01-15 14:30:00",
    "updateTime": "2024-01-15 14:30:00"
  }
}
```

### 2.5 保存模板打印记录

**接口地址**: `POST /api/v2/printRecord/saveTemplate`

**功能描述**: 模板打印时保存打印记录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| templateId | Integer | 是 | 模板ID |
| templateName | String | 是 | 模板名称 |
| templateCover | String | 否 | 模板封面URL |
| printWidth | Integer | 否 | 打印宽度 |
| printHeight | Integer | 否 | 打印高度 |
| printCopies | Integer | 否 | 打印份数，默认1 |
| printPlatform | Integer | 是 | 打印端类型 |
| templateData | String | 否 | 模板数据JSON |

**请求示例**:
```json
{
  "templateId": 123,
  "templateName": "商品标签模板",
  "templateCover": "http://example.com/cover.jpg",
  "printWidth": 50,
  "printHeight": 30,
  "printCopies": 5,
  "printPlatform": 1,
  "templateData": "{...}"
}
```

**响应示例**:
```json
{
  "state": "ok",
  "msg": "保存成功"
}
```

### 2.6 记录模板打印（集成接口）

**接口地址**: `POST /api/v2/templet/recordPrint`

**功能描述**: 在模板打印操作中记录打印行为

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| templateId | Integer | 是 | 模板ID |
| templateName | String | 是 | 模板名称 |
| templateCover | String | 否 | 模板封面URL |
| printWidth | Integer | 否 | 打印宽度 |
| printHeight | Integer | 否 | 打印高度 |
| printCopies | Integer | 否 | 打印份数，默认1 |
| printPlatform | Integer | 否 | 打印端类型（可自动检测） |
| templateData | String | 否 | 模板数据JSON |

**请求示例**:
```json
{
  "templateId": 123,
  "templateName": "商品标签模板",
  "templateCover": "http://example.com/cover.jpg",
  "printWidth": 50,
  "printHeight": 30,
  "printCopies": 5,
  "printPlatform": 1,
  "templateData": "{...}"
}
```

**响应示例**:
```json
{
  "state": "ok",
  "msg": "打印记录保存成功"
}
```

## 3. 数据字典

### 3.1 打印端类型 (printPlatform)

| 值 | 名称 | 说明 |
|----|------|------|
| 1 | 手机iOS | iPhone/iPad设备 |
| 2 | 手机安卓 | Android设备 |
| 3 | 电脑Windows | Windows PC |
| 4 | 电脑Mac | Mac电脑 |

### 3.2 打印状态 (printStatus)

| 值 | 名称 | 说明 |
|----|------|------|
| 1 | 成功 | 打印成功 |
| 2 | 失败 | 打印失败 |

## 4. 错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 40001 | 参数错误 | 检查请求参数是否正确 |
| 40002 | 打印记录不存在 | 检查记录ID是否有效 |
| 40003 | 无权限访问该记录 | 用户只能访问自己的记录 |
| 40004 | 用户未登录 | 需要先登录 |
| 50001 | 服务器内部错误 | 联系技术支持 |

## 5. 注意事项

1. 所有接口都需要用户登录认证
2. 用户只能访问自己的打印记录
3. 删除操作为软删除，不会物理删除数据
4. 打印记录按时间倒序排列
5. 模板打印记录默认展示100条，文档和图片打印记录默认展示20条
6. 搜索功能支持模糊匹配
7. 尺寸搜索仅模板打印支持
