# VIP购买业务流程与架构（v1）

## 核心流程
1. 前端发起下单：POST /api/v2/vip/order/create { plan: monthly|yearly, channel: wechat }
2. 后端创建订单（待支付）：生成订单号、金额、签名参数（根据渠道）返回给前端
3. 用户完成支付：微信支付调起并完成，异步通知（回调）到后端
4. 后端验签与查单确认成功：更新订单状态 paid，并触发“发货”逻辑：开通/续期 VIP
5. 返回结果：前端可轮询订单状态或通过回调/推送知晓

## 订单与发货（开通 VIP）
- 幂等约束：同一订单号只允许发货一次
- 续期规则：
  - 若当前用户非 VIP（FREE），则从当前时间开始计算有效期
  - 若当前用户是 VIP 且未过期，则在原到期时间基础上叠加
  - 月度：+30 天；年度：+365 天（或使用自然月/年，v1 采用固定天数）
- 用户模型更新：user.userTier、user.vipExpireTime（新增字段）

## 权限控制
- OCR 识图新建接口（OcrController.recognize）增加 VIP 功能检查：
  - 使用 VipPermissionService.hasPermission(userId, FEATURE_OCR_CREATE)
  - 如果无权限，返回 RetKit.fail("识图新建为VIP专享功能，前往开通") 并携带升级引导内容

## 系统架构
- Controller 层：VipOrderController（下单、查询）、VipPayCallbackController（回调）
- Service 层：VipOrderService（创建/查单）、VipPayService（聚合支付/微信签名）、VipDeliveryService（发货：开通VIP）
- DAO/Model：VipOrder（订单表）、User（扩展字段）

## 回调安全
- 使用微信支付 v3：
  - 验签：HTTP 头部 Wechatpay-Timestamp、Wechatpay-Nonce、Wechatpay-Signature、Wechatpay-Serial
  - 通知报文 resource 对称解密（API v3 key）
  - 二次查单确认（可选，提高安全性）
- 重试与幂等：回调可重复，多次调用发货时通过订单状态 + 唯一约束保证只发货一次

