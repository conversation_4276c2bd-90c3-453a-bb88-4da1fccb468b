package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BaseVipPayNotifyLog;

/**
 * VIP支付回调日志模型
 */
@SuppressWarnings("serial")
public class VipPayNotifyLog extends BaseVipPayNotifyLog<VipPayNotifyLog> {
	public static final VipPayNotifyLog dao = new VipPayNotifyLog().dao();

	// 验证结果常量
	public static final String VERIFY_SUCCESS = "success";
	public static final String VERIFY_FAILED = "failed";

	// 处理状态常量
	public static final String HANDLE_IGNORED = "ignored";
	public static final String HANDLE_PROCESSED = "processed";
	public static final String HANDLE_ERROR = "error";

	// 支付渠道常量
	public static final String CHANNEL_WECHAT = "wechat";
}
