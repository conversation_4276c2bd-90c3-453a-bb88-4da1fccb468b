package com.sandu.xinye.api.v2.ocr;

import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.Assert;

import java.io.File;
import java.util.*;

/**
 * 测试条形码与文本合并功能的修复
 * 主要测试：当TextIn将独立的条形码文本合并到包含其他内容的文本块中时，
 * 能否正确识别并设置showText属性
 */
public class BarcodeTextMergeFixTest {
    
    private ElementConverter elementConverter;
    
    @Before
    public void setUp() {
        elementConverter = ElementConverter.me;
    }
    
    @Test
    public void testBarcodeTextMergeWithRealData() {
        // 使用真实数据文件进行测试
        File jsonFile = new File("data/xxx茶庄-TextIn识别返回.json");
        File imageFile = new File("data/xxx茶庄.png");
        
        if (!jsonFile.exists() || !imageFile.exists()) {
            System.out.println("跳过测试：缺少测试数据文件");
            return;
        }
        
        try {
            // 读取真实的TextIn返回数据
            String jsonContent = new String(java.nio.file.Files.readAllBytes(jsonFile.toPath()));
            com.google.gson.Gson gson = new com.google.gson.Gson();
            java.util.Map<String, Object> jsonData = gson.fromJson(jsonContent, java.util.Map.class);
            
            TextInResponse textInResponse = new TextInResponse();
            textInResponse.setResult(jsonData);
            
            // 调用转换方法
            OcrResponse response = elementConverter.convertToXPrinterFormat(textInResponse, imageFile, 720, 540);
            
            // 验证结果
            Assert.assertNotNull("响应不应为空", response);
            Assert.assertNotNull("元素列表不应为空", response.getElements());
            Assert.assertTrue("应该有元素", response.getElements().size() > 0);
            
            // 打印所有元素信息
            System.out.println("总共识别到 " + response.getElements().size() + " 个元素:");
            for (int i = 0; i < response.getElements().size(); i++) {
                Map<String, Object> element = response.getElements().get(i);
                System.out.println("  [" + i + "] type=" + element.get("elementType") + 
                                 ", content='" + element.get("content") + "'" +
                                 ", showText=" + element.get("showText") +
                                 ", pos=[" + element.get("x") + "," + element.get("y") + "]" +
                                 ", size=[" + element.get("width") + "x" + element.get("height") + "]");
                
                // 如果是条形码，显示文本尺寸信息
                if ("2".equals(String.valueOf(element.get("elementType")))) {
                    System.out.println("       textWidth=" + element.get("textWidth") + 
                                     ", textHeight=" + element.get("textHeight"));
                }
            }
            
            // 查找条形码元素
            Map<String, Object> barcodeElement = null;
            for (Map<String, Object> element : response.getElements()) {
                if ("2".equals(String.valueOf(element.get("elementType")))) {
                    barcodeElement = element;
                    break; // 取第一个条形码元素
                }
            }
            
            Assert.assertNotNull("应该找到条形码元素", barcodeElement);
            
            // 验证条形码内容
            String content = (String) barcodeElement.get("content");
            Assert.assertEquals("条形码内容应该是161616326", "161616326", content);
            
            // 验证showText属性
            Object showTextObj = barcodeElement.get("showText");
            Assert.assertNotNull("showText不应为空", showTextObj);
            int showText = ((Number) showTextObj).intValue();
            
            // 验证textWidth和textHeight
            Object textWidthObj = barcodeElement.get("textWidth");
            Object textHeightObj = barcodeElement.get("textHeight");
            Assert.assertNotNull("textWidth不应为空", textWidthObj);
            Assert.assertNotNull("textHeight不应为空", textHeightObj);
            
            int textWidth = ((Number) textWidthObj).intValue();
            int textHeight = ((Number) textHeightObj).intValue();
            
            System.out.println("\n✅ 条形码元素详情:");
            System.out.println("  内容: " + content);
            System.out.println("  位置: x=" + barcodeElement.get("x") + ", y=" + barcodeElement.get("y"));
            System.out.println("  尺寸: " + barcodeElement.get("width") + "x" + barcodeElement.get("height"));
            System.out.println("  showText: " + showText + " (" + (showText == 1 ? "不显示" : showText == 2 ? "上方" : showText == 3 ? "下方" : "无效") + ")");
            System.out.println("  文本尺寸: " + textWidth + "x" + textHeight);
            
            // 验证showText应该是3（文本在下方）
            Assert.assertEquals("showText应该是3(文本在下方)", 3, showText);
            
            // 验证文本尺寸应该大于0（当有关联文本时）
            if (showText == 2 || showText == 3) {
                Assert.assertTrue("textWidth应该大于0", textWidth > 0);
                Assert.assertTrue("textHeight应该大于0", textHeight > 0);
            }
            
            System.out.println("✅ 测试通过：条形码成功识别到下方的文本");
            
        } catch (Exception e) {
            e.printStackTrace();
            Assert.fail("测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testBarcodeTextMergeWithPagesContent() {
        // 模拟TextIn返回的数据结构，基于真实的xxx茶庄.png数据
        TextInResponse textInResponse = new TextInResponse();
        
        // 创建result Map
        Map<String, Object> result = new HashMap<>();
        
        // 创建pages数据
        Map<String, Object> page = new HashMap<>();
        page.put("page_id", 1);
        page.put("width", 720);
        page.put("height", 540);
        
        // 创建content数组（包含独立的"161616326"文本）
        List<Map<String, Object>> content = new ArrayList<>();
        
        // content[0] - XXX茶庄
        Map<String, Object> content0 = new HashMap<>();
        content0.put("id", 0);
        content0.put("text", "XXX茶庄");
        content0.put("type", "line");
        content0.put("pos", Arrays.asList(240, 56, 408, 56, 408, 104, 240, 104));
        content.add(content0);
        
        // content[1] - 年份：2021冬
        Map<String, Object> content1 = new HashMap<>();
        content1.put("id", 1);
        content1.put("text", "年份：2021冬");
        content1.put("type", "line");
        content1.put("pos", Arrays.asList(24, 439, 229, 439, 229, 475, 24, 475));
        content.add(content1);
        
        // content[2] - 161616326 (这是关键的独立文本块)
        Map<String, Object> content2 = new HashMap<>();
        content2.put("id", 2);
        content2.put("text", "161616326");
        content2.put("type", "line");
        content2.put("pos", Arrays.asList(461, 428, 636, 428, 636, 461, 461, 461));
        content.add(content2);
        
        // content[3] - 条形码
        Map<String, Object> content3 = new HashMap<>();
        content3.put("id", 3);
        content3.put("type", "image");
        content3.put("sub_type", "barcode");
        content3.put("pos", Arrays.asList(423, 332, 668, 332, 669, 420, 423, 421));
        Map<String, Object> barcodeData = new HashMap<>();
        barcodeData.put("path", "https://web-api.textin.com/ocr_image/external/e2e7d816a9ef6d4a.jpg");
        content3.put("data", barcodeData);
        content.add(content3);
        
        page.put("content", content);
        
        // 创建detail数组（这里"年份：2021冬 161616326"被合并了）
        List<Map<String, Object>> detail = new ArrayList<>();
        
        Map<String, Object> detail0 = new HashMap<>();
        detail0.put("paragraph_id", 0);
        detail0.put("page_id", 1);
        detail0.put("text", "XXX茶庄");
        detail0.put("type", "paragraph");
        detail0.put("sub_type", "text_title");
        detail0.put("position", Arrays.asList(240, 57, 408, 57, 408, 101, 240, 101));
        detail0.put("content", 0);  // 引用content[0]
        detail.add(detail0);
        
        // detail中的合并文本块
        Map<String, Object> detail7 = new HashMap<>();
        detail7.put("paragraph_id", 7);
        detail7.put("page_id", 1);
        detail7.put("text", "年份：2021冬");  // 只包含年份部分，161616326是独立的
        detail7.put("type", "paragraph");
        detail7.put("sub_type", "text");
        detail7.put("position", Arrays.asList(24, 430, 229, 430, 229, 472, 24, 472));
        detail7.put("content", 1);  // 引用content[1] (年份部分)
        detail.add(detail7);
        
        // 添加独立的161616326文本元素
        Map<String, Object> detail10 = new HashMap<>();
        detail10.put("paragraph_id", 10);
        detail10.put("page_id", 1);
        detail10.put("text", "161616326");
        detail10.put("type", "paragraph");
        detail10.put("sub_type", "text");
        detail10.put("position", Arrays.asList(461, 428, 636, 428, 636, 461, 461, 461));
        detail10.put("content", 2);  // 引用content[2] (161616326)
        detail.add(detail10);
        
        // detail中的条形码
        Map<String, Object> detailBarcode = new HashMap<>();
        detailBarcode.put("paragraph_id", 5);
        detailBarcode.put("page_id", 1);
        detailBarcode.put("text", "");
        detailBarcode.put("type", "image");
        detailBarcode.put("sub_type", "barcode");
        detailBarcode.put("position", Arrays.asList(423, 332, 668, 332, 669, 420, 423, 421));
        detailBarcode.put("image_url", "https://web-api.textin.com/ocr_image/external/e2e7d816a9ef6d4a.jpg");
        detail.add(detailBarcode);
        
        // 将pages和detail添加到result中
        result.put("pages", Arrays.asList(page));
        result.put("detail", detail);
        result.put("success_count", 1);
        result.put("total_count", 1);
        result.put("total_page_number", 1);
        
        // 设置result到textInResponse
        textInResponse.setResult(result);
        
        // 创建模拟的图片文件
        File mockFile = new File("test-image.png");
        
        // 调用转换方法
        OcrResponse response = elementConverter.convertToXPrinterFormat(textInResponse, mockFile, 720, 540);
        
        // 验证结果
        Assert.assertNotNull("响应不应为空", response);
        Assert.assertNotNull("元素列表不应为空", response.getElements());
        Assert.assertTrue("应该有元素", response.getElements().size() > 0);
        
        // 打印所有元素信息
        System.out.println("总共识别到 " + response.getElements().size() + " 个元素:");
        for (int i = 0; i < response.getElements().size(); i++) {
            Map<String, Object> element = response.getElements().get(i);
            System.out.println("  [" + i + "] type=" + element.get("elementType") + 
                             ", content='" + element.get("content") + "'" +
                             ", showText=" + element.get("showText") +
                             ", pos=[" + element.get("x") + "," + element.get("y") + "]");
        }
        
        // 查找条形码元素
        Map<String, Object> barcodeElement = null;
        for (Map<String, Object> element : response.getElements()) {
            if ("2".equals(String.valueOf(element.get("elementType")))) {
                barcodeElement = element;
                break; // 取第一个条形码元素
            }
        }
        
        Assert.assertNotNull("应该找到条形码元素", barcodeElement);
        
        // 验证showText属性
        Object showTextObj = barcodeElement.get("showText");
        Assert.assertNotNull("showText不应为空", showTextObj);
        
        int showText = (Integer) showTextObj;
        System.out.println("\n✅ 条形码元素详情:");
        System.out.println("  内容: " + barcodeElement.get("content"));
        System.out.println("  位置: x=" + barcodeElement.get("x") + ", y=" + barcodeElement.get("y"));
        System.out.println("  showText: " + showText + " (" + (showText == 1 ? "不显示" : showText == 2 ? "上方" : showText == 3 ? "下方" : "无效") + ")");
        
        // 预期：文本"161616326"在条形码下方，所以showText应该是3
        Assert.assertEquals("showText应该是3(文本在下方)", 3, showText);
        
        System.out.println("✅ 测试通过：条形码成功识别到下方的文本，showText=" + showText);
    }
}