package com.sandu.xinye.vip;

import com.sandu.xinye.common.enums.UserTier;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.VipOrder;
import com.sandu.xinye.common.service.VipDeliveryService;
import org.junit.Test;

import java.util.Calendar;
import java.util.Date;

import static org.junit.Assert.*;

/**
 * VIP发货服务测试
 */
public class VipDeliveryServiceTest {
    
    /**
     * 测试首次开通VIP
     */
    @Test
    public void testFirstTimeVipDelivery() {
        // 模拟免费用户
        User user = new User();
        user.setUserTier(UserTier.FREE.name());
        user.setVipExpireTime(null);
        
        // 模拟月度订单
        VipOrder order = new VipOrder();
        order.setPlan(VipOrder.PLAN_MONTHLY);
        order.setTier(UserTier.VIP_MONTHLY.name());
        
        // 计算预期到期时间（从现在开始+1个月）
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.add(Calendar.MONTH, 1);
        Date expectedExpireTime = expectedCal.getTime();
        
        // 测试到期时间计算逻辑
        Date actualExpireTime = calculateNewExpireTime(user, order.getPlan());
        
        // 验证到期时间在合理范围内（允许几秒误差）
        long timeDiff = Math.abs(actualExpireTime.getTime() - expectedExpireTime.getTime());
        assertTrue("到期时间计算错误", timeDiff < 5000); // 5秒误差范围
    }
    
    /**
     * 测试VIP续期（叠加时长）
     */
    @Test
    public void testVipRenewal() {
        // 模拟当前VIP用户（还有10天到期）
        User user = new User();
        user.setUserTier(UserTier.VIP_MONTHLY.name());
        
        Calendar currentExpireCal = Calendar.getInstance();
        currentExpireCal.add(Calendar.DAY_OF_MONTH, 10);
        Date currentExpireTime = currentExpireCal.getTime();
        user.setVipExpireTime(currentExpireTime);
        
        // 模拟年度续费订单
        VipOrder order = new VipOrder();
        order.setPlan(VipOrder.PLAN_YEARLY);
        order.setTier(UserTier.VIP_YEARLY.name());
        
        // 计算预期到期时间（从当前到期时间+1年）
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(currentExpireTime);
        expectedCal.add(Calendar.YEAR, 1);
        Date expectedExpireTime = expectedCal.getTime();
        
        // 测试到期时间计算逻辑
        Date actualExpireTime = calculateNewExpireTime(user, order.getPlan());
        
        // 验证到期时间叠加正确
        long timeDiff = Math.abs(actualExpireTime.getTime() - expectedExpireTime.getTime());
        assertTrue("VIP续期时间计算错误", timeDiff < 5000);
    }
    
    /**
     * 测试过期VIP用户续费
     */
    @Test
    public void testExpiredVipRenewal() {
        // 模拟已过期的VIP用户
        User user = new User();
        user.setUserTier(UserTier.VIP_MONTHLY.name());
        
        Calendar expiredCal = Calendar.getInstance();
        expiredCal.add(Calendar.DAY_OF_MONTH, -5); // 5天前过期
        user.setVipExpireTime(expiredCal.getTime());
        
        // 模拟月度续费订单
        VipOrder order = new VipOrder();
        order.setPlan(VipOrder.PLAN_MONTHLY);
        order.setTier(UserTier.VIP_MONTHLY.name());
        
        // 计算预期到期时间（从现在开始+1个月，因为已过期）
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.add(Calendar.MONTH, 1);
        Date expectedExpireTime = expectedCal.getTime();
        
        // 测试到期时间计算逻辑
        Date actualExpireTime = calculateNewExpireTime(user, order.getPlan());
        
        // 验证从当前时间开始计算
        long timeDiff = Math.abs(actualExpireTime.getTime() - expectedExpireTime.getTime());
        assertTrue("过期VIP续费时间计算错误", timeDiff < 5000);
    }
    
    /**
     * 测试套餐类型验证
     */
    @Test
    public void testPlanValidation() {
        assertTrue("月度套餐验证失败", isValidPlan(VipOrder.PLAN_MONTHLY));
        assertTrue("年度套餐验证失败", isValidPlan(VipOrder.PLAN_YEARLY));
        assertFalse("无效套餐应该验证失败", isValidPlan("invalid_plan"));
        assertFalse("空套餐应该验证失败", isValidPlan(null));
    }
    
    /**
     * 测试用户等级转换
     */
    @Test
    public void testUserTierConversion() {
        assertEquals("月度套餐转换错误", UserTier.VIP_MONTHLY, planToTier(VipOrder.PLAN_MONTHLY));
        assertEquals("年度套餐转换错误", UserTier.VIP_YEARLY, planToTier(VipOrder.PLAN_YEARLY));
        assertEquals("无效套餐默认转换错误", UserTier.VIP_MONTHLY, planToTier("invalid"));
    }
    
    /**
     * 测试价格配置
     */
    @Test
    public void testPriceConfiguration() {
        assertEquals("月度价格错误", 990, getPlanPrice(VipOrder.PLAN_MONTHLY));
        assertEquals("年度价格错误", 9900, getPlanPrice(VipOrder.PLAN_YEARLY));
        assertEquals("无效套餐默认价格错误", 990, getPlanPrice("invalid"));
    }
    
    // ========== 辅助方法（模拟实际服务中的私有方法） ==========
    
    private Date calculateNewExpireTime(User user, String plan) {
        Date currentExpireTime = user.getVipExpireTime();
        Date now = new Date();
        
        Date startTime;
        if (currentExpireTime != null && currentExpireTime.after(now)) {
            startTime = currentExpireTime;
        } else {
            startTime = now;
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                calendar.add(Calendar.MONTH, 1);
                break;
            case VipOrder.PLAN_YEARLY:
                calendar.add(Calendar.YEAR, 1);
                break;
            default:
                calendar.add(Calendar.MONTH, 1);
        }
        
        return calendar.getTime();
    }
    
    private boolean isValidPlan(String plan) {
        return VipOrder.PLAN_MONTHLY.equals(plan) || VipOrder.PLAN_YEARLY.equals(plan);
    }
    
    private UserTier planToTier(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return UserTier.VIP_MONTHLY;
            case VipOrder.PLAN_YEARLY:
                return UserTier.VIP_YEARLY;
            default:
                return UserTier.VIP_MONTHLY;
        }
    }
    
    private int getPlanPrice(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return 990;
            case VipOrder.PLAN_YEARLY:
                return 9900;
            default:
                return 990;
        }
    }
}
