package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * VIP统计服务
 * 提供VIP相关的统计信息
 */
public class VipStatsService {
    
    public static final VipStatsService me = new VipStatsService();
    
    /**
     * 获取VIP用户统计
     */
    public Map<String, Object> getVipUserStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 总用户数
            Long totalUsers = Db.queryLong("SELECT COUNT(*) FROM user");
            stats.put("totalUsers", totalUsers);
            
            // VIP用户数
            Long vipUsers = Db.queryLong("SELECT COUNT(*) FROM user WHERE userTier != 'FREE'");
            stats.put("vipUsers", vipUsers);
            
            // 免费用户数
            stats.put("freeUsers", totalUsers - vipUsers);
            
            // VIP转化率
            double conversionRate = totalUsers > 0 ? (double) vipUsers / totalUsers * 100 : 0;
            stats.put("conversionRate", Math.round(conversionRate * 100.0) / 100.0);
            
            // 按等级统计
            List<Record> tierStats = Db.find("SELECT userTier, COUNT(*) as count FROM user GROUP BY userTier");
            Map<String, Long> tierCounts = new HashMap<>();
            for (Record record : tierStats) {
                tierCounts.put(record.getStr("userTier"), record.getLong("count"));
            }
            stats.put("tierStats", tierCounts);
            
            // 即将过期的VIP用户（7天内）
            Long expiringSoon = Db.queryLong(
                "SELECT COUNT(*) FROM user WHERE userTier != 'FREE' AND vipExpireTime BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)"
            );
            stats.put("expiringSoon", expiringSoon);
            
            return stats;
            
        } catch (Exception e) {
            LogKit.error("获取VIP用户统计失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取VIP订单统计
     */
    public Map<String, Object> getVipOrderStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 总订单数
            Long totalOrders = Db.queryLong("SELECT COUNT(*) FROM vip_order");
            stats.put("totalOrders", totalOrders);
            
            // 已支付订单数
            Long paidOrders = Db.queryLong("SELECT COUNT(*) FROM vip_order WHERE status = 'paid'");
            stats.put("paidOrders", paidOrders);
            
            // 待支付订单数
            Long pendingOrders = Db.queryLong("SELECT COUNT(*) FROM vip_order WHERE status = 'created'");
            stats.put("pendingOrders", pendingOrders);
            
            // 支付成功率
            double successRate = totalOrders > 0 ? (double) paidOrders / totalOrders * 100 : 0;
            stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
            
            // 按套餐统计
            List<Record> planStats = Db.find("SELECT plan, COUNT(*) as count FROM vip_order WHERE status = 'paid' GROUP BY plan");
            Map<String, Long> planCounts = new HashMap<>();
            for (Record record : planStats) {
                planCounts.put(record.getStr("plan"), record.getLong("count"));
            }
            stats.put("planStats", planCounts);
            
            // 总收入（分）
            Long totalRevenue = Db.queryLong("SELECT COALESCE(SUM(amount), 0) FROM vip_order WHERE status = 'paid'");
            stats.put("totalRevenue", totalRevenue);
            stats.put("totalRevenueYuan", totalRevenue / 100.0);
            
            // 今日订单数
            Long todayOrders = Db.queryLong("SELECT COUNT(*) FROM vip_order WHERE DATE(create_time) = CURDATE()");
            stats.put("todayOrders", todayOrders);
            
            // 今日收入
            Long todayRevenue = Db.queryLong("SELECT COALESCE(SUM(amount), 0) FROM vip_order WHERE status = 'paid' AND DATE(paid_time) = CURDATE()");
            stats.put("todayRevenue", todayRevenue);
            stats.put("todayRevenueYuan", todayRevenue / 100.0);
            
            return stats;
            
        } catch (Exception e) {
            LogKit.error("获取VIP订单统计失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取VIP功能使用统计
     */
    public Map<String, Object> getVipFeatureStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // VIP功能规则数
            Long ruleCount = Db.queryLong("SELECT COUNT(*) FROM vip_feature_rule WHERE enabled = 1");
            stats.put("enabledRules", ruleCount);
            
            // 回调日志统计
            Long callbackCount = Db.queryLong("SELECT COUNT(*) FROM vip_pay_notify_log");
            stats.put("totalCallbacks", callbackCount);
            
            Long successCallbacks = Db.queryLong("SELECT COUNT(*) FROM vip_pay_notify_log WHERE verify_result = 'success'");
            stats.put("successCallbacks", successCallbacks);
            
            // 发货日志统计
            Long deliveryCount = Db.queryLong("SELECT COUNT(*) FROM vip_delivery_log");
            stats.put("totalDeliveries", deliveryCount);
            
            return stats;
            
        } catch (Exception e) {
            LogKit.error("获取VIP功能统计失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 获取完整的VIP系统统计
     */
    public Map<String, Object> getCompleteStats() {
        Map<String, Object> completeStats = new HashMap<>();
        
        completeStats.put("userStats", getVipUserStats());
        completeStats.put("orderStats", getVipOrderStats());
        completeStats.put("featureStats", getVipFeatureStats());
        completeStats.put("timestamp", new java.util.Date());
        
        return completeStats;
    }
}
