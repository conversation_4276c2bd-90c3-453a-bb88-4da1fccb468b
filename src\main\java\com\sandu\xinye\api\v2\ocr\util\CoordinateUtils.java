package com.sandu.xinye.api.v2.ocr.util;

import java.text.DecimalFormat;
import java.util.List;

/**
 * 坐标转换工具类
 * 处理像素坐标与毫米坐标之间的转换
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class CoordinateUtils {
    
    // 默认DPI值，用于像素到毫米的转换
    private static final double DEFAULT_DPI = 300.0;
    
    // 毫米到英寸的转换系数
    private static final double MM_PER_INCH = 25.4;
    
    // 数字格式化器，保留合适的小数位数
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.######");
    
    /**
     * 将像素坐标转换为毫米坐标
     * 
     * @param pixels 像素值
     * @param dpi DPI值，如果为null则使用默认值300
     * @return 毫米值的字符串表示
     */
    public static String pixelsToMm(int pixels, Double dpi) {
        double actualDpi = dpi != null ? dpi : DEFAULT_DPI;
        double mm = (pixels * MM_PER_INCH) / actualDpi;
        return DECIMAL_FORMAT.format(mm);
    }
    
    /**
     * 将像素坐标转换为毫米坐标（使用默认DPI）
     * 
     * @param pixels 像素值
     * @return 毫米值的字符串表示
     */
    public static String pixelsToMm(int pixels) {
        return pixelsToMm(pixels, null);
    }
    
    /**
     * 将毫米坐标转换为像素坐标
     * 
     * @param mm 毫米值
     * @param dpi DPI值，如果为null则使用默认值300
     * @return 像素值
     */
    public static int mmToPixels(double mm, Double dpi) {
        double actualDpi = dpi != null ? dpi : DEFAULT_DPI;
        return (int) Math.round((mm * actualDpi) / MM_PER_INCH);
    }
    
    /**
     * 将毫米坐标转换为像素坐标（使用默认DPI）
     * 
     * @param mm 毫米值
     * @return 像素值
     */
    public static int mmToPixels(double mm) {
        return mmToPixels(mm, null);
    }
    
    /**
     * 根据字符宽度计算字体大小
     * 这是一个经验公式，可能需要根据实际情况调整
     * 
     * @param charWidthPx 字符宽度（像素）
     * @return 字体大小的字符串表示
     */
    public static String calculateFontSize(double charWidthPx) {
        // 经验公式：字体大小 ≈ 字符宽度 * 0.75
        double fontSize = charWidthPx * 0.75;
        
        // 限制字体大小范围
        if (fontSize < 6.0) {
            fontSize = 6.0;
        } else if (fontSize > 72.0) {
            fontSize = 72.0;
        }
        
        return DECIMAL_FORMAT.format(fontSize);
    }
    
    /**
     * 计算文本区域的平均字符宽度
     * 
     * @param textWidth 文本区域宽度（像素）
     * @param textLength 文本长度（字符数）
     * @return 平均字符宽度（像素）
     */
    public static double calculateCharWidth(int textWidth, int textLength) {
        if (textLength <= 0) {
            return 0.0;
        }
        return (double) textWidth / textLength;
    }
    
    /**
     * 格式化数字为字符串，去除不必要的小数位
     * 
     * @param value 数值
     * @return 格式化后的字符串
     */
    public static String formatNumber(double value) {
        return DECIMAL_FORMAT.format(value);
    }
    
    /**
     * 检查坐标是否在合理范围内
     * 
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param imageWidth 图片宽度
     * @param imageHeight 图片高度
     * @return 是否在合理范围内
     */
    public static boolean isValidBounds(int x, int y, int width, int height, 
                                       int imageWidth, int imageHeight) {
        return x >= 0 && y >= 0 && 
               x + width <= imageWidth && 
               y + height <= imageHeight &&
               width > 0 && height > 0;
    }
    
    /**
     * 基于TextIn char_positions数据智能计算字符宽度
     * 
     * @param text 文本内容
     * @param charPositions 每个字符的四角坐标数组
     * @return 智能计算的平均字符宽度
     */
    @SuppressWarnings("unchecked")
    public static double calculateSmartCharWidth(String text, List<List<Integer>> charPositions) {
        if (text == null || text.isEmpty() || charPositions == null || charPositions.isEmpty()) {
            return 0.0;
        }
        
        // 确保字符数和位置数一致
        int minLength = Math.min(text.length(), charPositions.size());
        if (minLength == 0) {
            return 0.0;
        }
        
        // 字符分类统计
        int chineseCount = 0, englishCount = 0, digitCount = 0, punctuationCount = 0;
        double chineseWidthSum = 0, englishWidthSum = 0, digitWidthSum = 0, punctuationWidthSum = 0;
        
        for (int i = 0; i < minLength; i++) {
            char ch = text.charAt(i);
            List<Integer> charPos = charPositions.get(i);
            
            // 计算该字符的实际宽度
            double charWidth = calculateSingleCharWidth(charPos);
            if (charWidth <= 0) {
                continue; // 跳过无效宽度的字符
            }
            
            // 按字符类型分类统计
            if (isChinese(ch)) {
                chineseCount++;
                chineseWidthSum += charWidth;
            } else if (isEnglish(ch)) {
                englishCount++;
                englishWidthSum += charWidth;
            } else if (isDigit(ch)) {
                digitCount++;
                digitWidthSum += charWidth;
            } else if (isPunctuation(ch)) {
                punctuationCount++;
                punctuationWidthSum += charWidth;
            }
        }
        
        // 智能策略选择
        double result = selectOptimalCharWidth(chineseCount, chineseWidthSum,
                                             englishCount, englishWidthSum,
                                             digitCount, digitWidthSum,
                                             punctuationCount, punctuationWidthSum);
        
        return Math.round(result * 10.0) / 10.0; // 保留1位小数
    }
    
    /**
     * 从单个字符的四角坐标计算宽度
     * 
     * @param charPosition 字符的四角坐标 [x1, y1, x2, y2, x3, y3, x4, y4]
     * @return 字符宽度（像素）
     */
    private static double calculateSingleCharWidth(List<Integer> charPosition) {
        if (charPosition == null || charPosition.size() < 8) {
            return 0.0;
        }
        
        // 提取所有X坐标
        int x1 = charPosition.get(0), x2 = charPosition.get(2);
        int x3 = charPosition.get(4), x4 = charPosition.get(6);
        
        // 计算最小和最大X坐标
        int minX = Math.min(Math.min(x1, x2), Math.min(x3, x4));
        int maxX = Math.max(Math.max(x1, x2), Math.max(x3, x4));
        
        return maxX - minX;
    }
    
    /**
     * 根据字符类型统计选择最优的字符宽度
     */
    private static double selectOptimalCharWidth(int chineseCount, double chineseWidthSum,
                                               int englishCount, double englishWidthSum,
                                               int digitCount, double digitWidthSum,
                                               int punctuationCount, double punctuationWidthSum) {
        
        // 策略1：存在中文字符，以中文为准（中文通常等宽且稳定）
        if (chineseCount > 0) {
            return chineseWidthSum / chineseCount;
        }
        
        // 策略2：数字占多数，以数字为准（数字通常等宽）
        if (digitCount > 0 && digitCount >= englishCount) {
            return digitWidthSum / digitCount;
        }
        
        // 策略3：英文占多数，以英文为准
        if (englishCount > 0) {
            return englishWidthSum / englishCount;
        }
        
        // 策略4：只有标点符号，以标点为准
        if (punctuationCount > 0) {
            return punctuationWidthSum / punctuationCount;
        }
        
        // 默认返回0
        return 0.0;
    }
    
    /**
     * 判断字符是否为中文
     */
    private static boolean isChinese(char ch) {
        return (ch >= 0x4E00 && ch <= 0x9FFF) ||     // CJK统一汉字
               (ch >= 0x3400 && ch <= 0x4DBF) ||     // CJK扩展A
               (ch >= 0x20000 && ch <= 0x2A6DF) ||   // CJK扩展B
               (ch >= 0x2A700 && ch <= 0x2B73F) ||   // CJK扩展C
               (ch >= 0x2B740 && ch <= 0x2B81F) ||   // CJK扩展D
               (ch >= 0x2B820 && ch <= 0x2CEAF) ||   // CJK扩展E
               (ch >= 0x3000 && ch <= 0x303F);       // CJK符号和标点
    }
    
    /**
     * 判断字符是否为英文字母
     */
    private static boolean isEnglish(char ch) {
        return (ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z');
    }
    
    /**
     * 判断字符是否为数字
     */
    private static boolean isDigit(char ch) {
        return ch >= '0' && ch <= '9';
    }
    
    /**
     * 判断字符是否为标点符号
     */
    private static boolean isPunctuation(char ch) {
        return !isChinese(ch) && !isEnglish(ch) && !isDigit(ch) && !Character.isWhitespace(ch);
    }
    
    /**
     * 基于TextIn char_positions数据智能判断文本是否为斜体
     * 通过计算字符的几何倾斜角度来判断
     * 
     * @param text 文本内容
     * @param charPositions 每个字符的四角坐标数组
     * @return 是否为斜体
     */
    @SuppressWarnings("unchecked")
    public static boolean calculateItalicFromGeometry(String text, List<List<Integer>> charPositions) {
        if (text == null || text.isEmpty() || charPositions == null || charPositions.isEmpty()) {
            return false;
        }
        
        int validCharCount = 0;
        double totalItalicAngle = 0.0;
        
        // 分析每个字符的倾斜角度
        int minLength = Math.min(text.length(), charPositions.size());
        for (int i = 0; i < minLength; i++) {
            List<Integer> charPos = charPositions.get(i);
            if (charPos == null || charPos.size() < 8) {
                continue;
            }
            
            double italicAngle = calculateCharItalicAngle(charPos);
            if (!Double.isNaN(italicAngle)) {
                totalItalicAngle += Math.abs(italicAngle);
                validCharCount++;
            }
        }
        
        // 需要至少2个有效字符才能判断
        if (validCharCount < 2) {
            return false;
        }
        
        // 计算平均倾斜角度
        double avgItalicAngle = totalItalicAngle / validCharCount;
        
        // 设定斜体判断阈值：平均倾斜角度超过12度视为斜体
        final double ITALIC_THRESHOLD = 12.0;
        boolean isItalic = avgItalicAngle > ITALIC_THRESHOLD;
        
        if (isItalic) {
            System.out.println("几何分析判断为斜体: 平均倾斜角度=" + String.format("%.1f", avgItalicAngle) + 
                             "°, 有效字符数=" + validCharCount + ", 文本=" + text);
        }
        
        return isItalic;
    }
    
    /**
     * 计算单个字符的倾斜角度
     * 基于四角坐标的几何分析
     * 
     * @param charPosition 字符的四角坐标 [x1, y1, x2, y2, x3, y3, x4, y4]
     * @return 倾斜角度（度），NaN表示无法计算
     */
    private static double calculateCharItalicAngle(List<Integer> charPosition) {
        if (charPosition == null || charPosition.size() < 8) {
            return Double.NaN;
        }
        
        try {
            // TextIn四角坐标通常按顺序：左上 -> 右上 -> 右下 -> 左下
            int x1 = charPosition.get(0), y1 = charPosition.get(1); // 左上
            int x2 = charPosition.get(2), y2 = charPosition.get(3); // 右上
            int x3 = charPosition.get(4), y3 = charPosition.get(5); // 右下
            int x4 = charPosition.get(6), y4 = charPosition.get(7); // 左下
            
            // 方法1：计算左边线的倾斜角度 (左上 -> 左下)
            double leftSlope = (double)(y4 - y1) / (x4 - x1);
            double leftAngle = Math.atan(leftSlope) * 180 / Math.PI;
            
            // 方法2：计算右边线的倾斜角度 (右上 -> 右下)
            double rightSlope = (double)(y3 - y2) / (x3 - x2);
            double rightAngle = Math.atan(rightSlope) * 180 / Math.PI;
            
            // 取左右边线角度的平均值，更稳定
            double avgAngle = (leftAngle + rightAngle) / 2.0;
            
            // 正常字符应该接近垂直（90度），斜体字符会偏离
            // 转换为相对于垂直线的偏离角度
            double italicAngle = Math.abs(90.0 - Math.abs(avgAngle));
            
            return italicAngle;
            
        } catch (ArithmeticException e) {
            // 除零或其他计算错误
            return Double.NaN;
        }
    }
}
