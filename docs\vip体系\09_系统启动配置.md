# VIP体系系统启动配置

## 应用启动时初始化

在应用的主启动类或配置类中添加VIP系统初始化：

```java
// 在应用启动完成后调用
public void afterJFinalStart() {
    // 初始化VIP系统
    VipSystemInitService.me.init();
}
```

## 定时任务配置

### 1. VIP过期处理任务

建议使用 Cron 表达式配置定时任务，每天凌晨执行：

```java
// 示例：使用 Quartz 或其他定时任务框架
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
public void processExpiredVip() {
    VipExpireTask.processExpiredVipUsers();
}
```

或者在现有的定时任务系统中添加：

```java
// 在定时任务配置中添加
public void configTasks() {
    // 每天凌晨2点处理过期VIP用户
    TaskKit.addTask("vip-expire", "0 0 2 * * ?", () -> {
        VipExpireTask.processExpiredVipUsers();
    });
}
```

### 2. 统计数据更新（可选）

```java
// 每小时更新一次VIP统计数据
@Scheduled(cron = "0 0 * * * ?")
public void updateVipStats() {
    try {
        Map<String, Object> stats = VipStatsService.me.getCompleteStats();
        // 可以将统计数据存储到缓存或数据库
        LogKit.info("VIP统计数据更新完成");
    } catch (Exception e) {
        LogKit.error("VIP统计数据更新失败", e);
    }
}
```

## 配置文件示例

### common_config.txt 配置示例

```properties
# ========== VIP体系配置 ==========

# 微信支付配置
wechat.pay.appid=wx1234567890abcdef
wechat.pay.mchid=1234567890
wechat.pay.api.v3.key=your_api_v3_key_32_characters_long
wechat.pay.serial.no=1234567890ABCDEF1234567890ABCDEF12345678
wechat.pay.private.key.path=certs/wechat_pay_private_key.pem
wechat.pay.notify.url=https://your-domain.com/api/v2/vip/pay/wechat/notify

# VIP功能开关（可选）
vip.feature.enabled=true
vip.order.enabled=true
vip.callback.log.enabled=true

# VIP套餐价格配置（可选，代码中已硬编码）
vip.price.monthly=990
vip.price.yearly=9900
```

### 开发环境配置

```properties
# 开发环境 - 使用模拟支付
wechat.pay.appid=wx_dev_test
wechat.pay.mchid=dev_test
wechat.pay.api.v3.key=dev_test_key_32_characters_long
wechat.pay.serial.no=dev_test_serial_number
wechat.pay.private.key.path=certs/dev_private_key.pem
wechat.pay.notify.url=http://localhost:8080/api/v2/vip/pay/wechat/notify
```

### 生产环境配置

```properties
# 生产环境 - 真实微信支付配置
wechat.pay.appid=wx_prod_real_appid
wechat.pay.mchid=prod_real_mchid
wechat.pay.api.v3.key=prod_real_api_v3_key_32_chars
wechat.pay.serial.no=prod_real_serial_number
wechat.pay.private.key.path=/secure/certs/wechat_pay_private_key.pem
wechat.pay.notify.url=https://api.yourapp.com/api/v2/vip/pay/wechat/notify
```

## 日志配置

### log4j.properties 配置示例

```properties
# VIP相关日志配置
log4j.logger.com.sandu.xinye.common.service.VipOrderService=INFO
log4j.logger.com.sandu.xinye.common.service.WechatPayService=INFO
log4j.logger.com.sandu.xinye.common.service.WechatPayCallbackService=INFO
log4j.logger.com.sandu.xinye.common.service.VipDeliveryService=INFO
log4j.logger.com.sandu.xinye.common.interceptor.VipFeatureInterceptor=DEBUG

# 支付回调日志单独文件
log4j.appender.vipPayCallback=org.apache.log4j.DailyRollingFileAppender
log4j.appender.vipPayCallback.File=logs/vip_pay_callback.log
log4j.appender.vipPayCallback.DatePattern='.'yyyy-MM-dd
log4j.appender.vipPayCallback.layout=org.apache.log4j.PatternLayout
log4j.appender.vipPayCallback.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} [%p] %c{1} - %m%n

log4j.logger.com.sandu.xinye.api.v2.vip.VipPayCallbackController=INFO, vipPayCallback
log4j.additivity.com.sandu.xinye.api.v2.vip.VipPayCallbackController=false
```

## 数据库连接池配置

确保数据库连接池能够支持VIP相关的并发操作：

```properties
# 数据库连接池配置
jdbcUrl=**************************************************************************************************
user=your_username
password=your_password
driverClass=com.mysql.cj.jdbc.Driver

# 连接池大小
initialPoolSize=10
minPoolSize=5
maxPoolSize=50
maxIdleTime=300
acquireIncrement=5
```

## 缓存配置（可选）

如果使用 EhCache 缓存VIP功能规则：

```xml
<!-- ehcache.xml -->
<cache name="vip_feature_rules"
       maxElementsInMemory="1000"
       eternal="false"
       timeToIdleSeconds="300"
       timeToLiveSeconds="600"
       overflowToDisk="false"
       memoryStoreEvictionPolicy="LRU" />
```

## 启动检查脚本

创建启动后的健康检查脚本：

```bash
#!/bin/bash
# vip_health_check.sh

echo "检查VIP体系健康状态..."

# 检查数据库表
mysql -u$DB_USER -p$DB_PASS -e "SELECT COUNT(*) FROM vip_feature_rule;" $DB_NAME

# 检查VIP状态接口
curl -s "http://localhost:8080/api/v2/vip/plans" | grep -q "monthly"

if [ $? -eq 0 ]; then
    echo "VIP体系启动正常"
else
    echo "VIP体系启动异常"
    exit 1
fi
```

## 注意事项

1. **证书文件安全**：确保私钥文件权限设置为 600，只有应用用户可读
2. **配置加密**：生产环境建议对敏感配置进行加密
3. **环境隔离**：开发、测试、生产环境使用不同的微信支付配置
4. **监控告警**：配置VIP相关的监控和告警规则
5. **备份策略**：定期备份VIP相关的数据和配置
