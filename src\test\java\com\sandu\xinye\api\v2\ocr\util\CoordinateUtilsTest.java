package com.sandu.xinye.api.v2.ocr.util;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 坐标转换工具类单元测试
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class CoordinateUtilsTest {
    
    @Test
    public void testPixelsToMm_DefaultDpi() {
        // 测试默认DPI下的像素到毫米转换
        String result = CoordinateUtils.pixelsToMm(300);
        
        // 300像素在300DPI下应该是25.4毫米（1英寸）
        assertEquals("25.4", result);
    }
    
    @Test
    public void testPixelsToMm_CustomDpi() {
        // 测试自定义DPI下的像素到毫米转换
        String result = CoordinateUtils.pixelsToMm(150, 150.0);
        
        // 150像素在150DPI下应该是25.4毫米（1英寸）
        assertEquals("25.4", result);
    }
    
    @Test
    public void testPixelsToMm_ZeroPixels() {
        // 测试0像素的转换
        String result = CoordinateUtils.pixelsToMm(0);
        
        assertEquals("0", result);
    }
    
    @Test
    public void testMmToPixels_DefaultDpi() {
        // 测试默认DPI下的毫米到像素转换
        int result = CoordinateUtils.mmToPixels(25.4);
        
        // 25.4毫米在300DPI下应该是300像素
        assertEquals(300, result);
    }
    
    @Test
    public void testMmToPixels_CustomDpi() {
        // 测试自定义DPI下的毫米到像素转换
        int result = CoordinateUtils.mmToPixels(25.4, 150.0);
        
        // 25.4毫米在150DPI下应该是150像素
        assertEquals(150, result);
    }
    
    @Test
    public void testMmToPixels_ZeroMm() {
        // 测试0毫米的转换
        int result = CoordinateUtils.mmToPixels(0.0);
        
        assertEquals(0, result);
    }
    
    @Test
    public void testCalculateFontSize_NormalCharWidth() {
        // 测试正常字符宽度的字体大小计算
        String result = CoordinateUtils.calculateFontSize(16.0);
        
        // 16像素宽度 * 0.75 = 12像素字体大小
        assertEquals("12", result);
    }
    
    @Test
    public void testCalculateFontSize_SmallCharWidth() {
        // 测试小字符宽度的字体大小计算（应该限制最小值）
        String result = CoordinateUtils.calculateFontSize(4.0);
        
        // 4像素宽度 * 0.75 = 3像素，但最小限制为6像素
        assertEquals("6", result);
    }
    
    @Test
    public void testCalculateFontSize_LargeCharWidth() {
        // 测试大字符宽度的字体大小计算（应该限制最大值）
        String result = CoordinateUtils.calculateFontSize(100.0);
        
        // 100像素宽度 * 0.75 = 75像素，但最大限制为72像素
        assertEquals("72", result);
    }
    
    @Test
    public void testCalculateCharWidth_NormalText() {
        // 测试正常文本的字符宽度计算
        double result = CoordinateUtils.calculateCharWidth(100, 5);
        
        // 100像素宽度 / 5个字符 = 20像素/字符
        assertEquals(20.0, result, 0.001);
    }
    
    @Test
    public void testCalculateCharWidth_ZeroLength() {
        // 测试零长度文本的字符宽度计算
        double result = CoordinateUtils.calculateCharWidth(100, 0);
        
        assertEquals(0.0, result, 0.001);
    }
    
    @Test
    public void testCalculateCharWidth_NegativeLength() {
        // 测试负长度文本的字符宽度计算
        double result = CoordinateUtils.calculateCharWidth(100, -1);
        
        assertEquals(0.0, result, 0.001);
    }
    
    @Test
    public void testFormatNumber_Integer() {
        // 测试整数格式化
        String result = CoordinateUtils.formatNumber(10.0);
        
        assertEquals("10", result);
    }
    
    @Test
    public void testFormatNumber_Decimal() {
        // 测试小数格式化
        String result = CoordinateUtils.formatNumber(10.123456);
        
        assertEquals("10.123456", result);
    }
    
    @Test
    public void testFormatNumber_VerySmallDecimal() {
        // 测试很小的小数格式化
        String result = CoordinateUtils.formatNumber(0.000001);
        
        assertEquals("0.000001", result);
    }
    
    @Test
    public void testIsValidBounds_ValidBounds() {
        // 测试有效边界
        boolean result = CoordinateUtils.isValidBounds(10, 20, 100, 50, 800, 600);
        
        assertTrue(result);
    }
    
    @Test
    public void testIsValidBounds_NegativeCoordinates() {
        // 测试负坐标
        boolean result = CoordinateUtils.isValidBounds(-10, 20, 100, 50, 800, 600);
        
        assertFalse(result);
    }
    
    @Test
    public void testIsValidBounds_ExceedsImageBounds() {
        // 测试超出图片边界
        boolean result = CoordinateUtils.isValidBounds(700, 20, 200, 50, 800, 600);
        
        assertFalse(result); // 700 + 200 = 900 > 800
    }
    
    @Test
    public void testIsValidBounds_ZeroSize() {
        // 测试零尺寸
        boolean result = CoordinateUtils.isValidBounds(10, 20, 0, 50, 800, 600);
        
        assertFalse(result);
    }
    
    @Test
    public void testIsValidBounds_NegativeSize() {
        // 测试负尺寸
        boolean result = CoordinateUtils.isValidBounds(10, 20, 100, -50, 800, 600);
        
        assertFalse(result);
    }
    
    @Test
    public void testIsValidBounds_EdgeCase() {
        // 测试边界情况
        boolean result = CoordinateUtils.isValidBounds(0, 0, 800, 600, 800, 600);
        
        assertTrue(result); // 正好填满整个图片
    }
}
