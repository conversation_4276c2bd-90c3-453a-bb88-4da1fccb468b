# VIP体系部署检查清单

## 数据库准备

### 1. 执行SQL脚本
- [ ] user 表新增 vipExpireTime 字段
- [ ] 创建 vip_order 表
- [ ] 创建 vip_feature_rule 表
- [ ] 创建 vip_delivery_log 表（可选）
- [ ] 创建 vip_pay_notify_log 表（可选）

### 2. 初始化数据
- [ ] 插入OCR识图VIP规则到 vip_feature_rule 表
- [ ] 验证表结构和索引正确创建

## 微信支付配置

### 1. 商户资质
- [ ] 微信支付商户号已申请并审核通过
- [ ] 微信开放平台应用已创建并获得AppID
- [ ] API证书已下载（包含私钥文件）

### 2. 配置文件
- [ ] 在 common_config.txt 中添加微信支付配置
- [ ] 私钥文件放置到指定路径
- [ ] 配置回调通知地址（HTTPS且外网可访问）

### 3. 配置项检查
```
wechat.pay.appid=wx1234567890abcdef
wechat.pay.mchid=1234567890
wechat.pay.api.v3.key=your_api_v3_key_32_characters_long
wechat.pay.serial.no=1234567890ABCDEF1234567890ABCDEF12345678
wechat.pay.private.key.path=certs/wechat_pay_private_key.pem
wechat.pay.notify.url=https://your-domain.com/api/v2/vip/pay/wechat/notify
```

## 代码部署

### 1. 模型映射
- [ ] _MappingKit.java 已注册新表映射
- [ ] 新增模型类已编译无错误

### 2. 路由配置
- [ ] v2/ApiRoutes 已注册VIP相关控制器
- [ ] VipFeatureInterceptor 已添加到拦截器链

### 3. 服务初始化
- [ ] 在应用启动时调用 VipSystemInitService.me.init()
- [ ] 微信支付服务初始化成功

## 功能验证

### 1. 权限拦截
- [ ] 非VIP用户访问OCR接口被拦截
- [ ] 返回正确的VIP升级提示

### 2. 订单流程
- [ ] 可以成功创建VIP订单
- [ ] 订单查询接口正常
- [ ] 支付参数生成正确

### 3. 回调处理
- [ ] 微信支付回调接口可访问
- [ ] 回调验签逻辑正确（生产环境）
- [ ] 订单状态更新正常
- [ ] VIP开通/续期逻辑正确

### 4. 状态查询
- [ ] VIP状态查询接口正常
- [ ] 套餐信息接口正常
- [ ] 用户VIP信息显示正确

## 安全检查

### 1. 敏感信息保护
- [ ] API v3密钥未提交到代码仓库
- [ ] 私钥文件权限设置正确
- [ ] 日志中敏感信息已脱敏

### 2. 接口安全
- [ ] 支付回调接口验签正确
- [ ] 订单创建有防重复检查
- [ ] 用户权限校验正确

## 监控与日志

### 1. 日志配置
- [ ] VIP相关操作有详细日志
- [ ] 支付回调有审计日志
- [ ] 错误日志级别配置正确

### 2. 监控指标
- [ ] VIP转化率监控
- [ ] 支付成功率监控
- [ ] 接口响应时间监控

## 定时任务

### 1. VIP过期处理
- [ ] 配置定时任务处理过期VIP用户
- [ ] 建议每天执行一次
- [ ] 任务执行日志正常

### 2. 统计数据
- [ ] 可选：配置VIP统计数据定时更新

## 测试验证

### 1. 单元测试
- [ ] VIP权限校验逻辑测试
- [ ] 到期时间计算逻辑测试
- [ ] 订单状态流转测试

### 2. 集成测试
- [ ] 完整的购买流程测试
- [ ] 权限拦截功能测试
- [ ] 回调处理幂等性测试

### 3. 压力测试
- [ ] 高并发下单测试
- [ ] 回调处理性能测试

## 上线准备

### 1. 备份
- [ ] 数据库备份
- [ ] 配置文件备份
- [ ] 代码版本标记

### 2. 回滚方案
- [ ] 准备回滚SQL脚本
- [ ] 准备代码回滚方案
- [ ] 确认回滚流程

### 3. 发布计划
- [ ] 确定发布时间窗口
- [ ] 准备发布公告
- [ ] 安排值班人员

## 上线后检查

### 1. 功能验证
- [ ] 生产环境功能正常
- [ ] 微信支付流程正常
- [ ] 用户反馈正常

### 2. 数据监控
- [ ] 订单数据正常
- [ ] 支付成功率正常
- [ ] 错误率在预期范围内

### 3. 性能监控
- [ ] 接口响应时间正常
- [ ] 数据库性能正常
- [ ] 服务器资源使用正常
