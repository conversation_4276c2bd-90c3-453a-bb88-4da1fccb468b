package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BaseVipOrder;
import com.sandu.xinye.common.enums.UserTier;

/**
 * VIP订单模型
 */
@SuppressWarnings("serial")
public class VipOrder extends BaseVipOrder<VipOrder> {
	public static final VipOrder dao = new VipOrder().dao();

	// 订单状态常量
	public static final String STATUS_CREATED = "created";
	public static final String STATUS_PAID = "paid";
	public static final String STATUS_CLOSED = "closed";
	public static final String STATUS_FAILED = "failed";

	// 套餐类型常量
	public static final String PLAN_MONTHLY = "monthly";
	public static final String PLAN_YEARLY = "yearly";

	// 支付渠道常量
	public static final String CHANNEL_WECHAT = "wechat";

	/**
	 * 获取用户等级枚举
	 */
	public UserTier getTierEnum() {
		String tierStr = getTier();
		return UserTier.fromString(tierStr);
	}

	/**
	 * 判断订单是否已支付
	 */
	public boolean isPaid() {
		return STATUS_PAID.equals(getStatus());
	}

	/**
	 * 判断订单是否可支付
	 */
	public boolean canPay() {
		return STATUS_CREATED.equals(getStatus());
	}

	/**
	 * 获取金额（元）
	 */
	public double getAmountYuan() {
		Integer amount = getAmount();
		return amount == null ? 0.0 : amount / 100.0;
	}

	/**
	 * 设置金额（元）
	 */
	public VipOrder setAmountYuan(double yuan) {
		return setAmount((int) Math.round(yuan * 100));
	}
}
