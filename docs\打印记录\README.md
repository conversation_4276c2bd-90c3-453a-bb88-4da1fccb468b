# XPrinter 打印记录功能

## 📋 项目概述

XPrinter打印记录功能是为了满足用户查看和管理打印历史的需求而开发的功能模块。该功能支持记录用户的模板打印、文档打印和图片打印行为，并提供查询、搜索、删除等管理功能。

## 🎯 功能特性

### ✅ 已实现功能（一期）

- **模板打印记录**
  - 记录模板打印行为
  - 分页查询打印记录列表（默认100条）
  - 按名称、尺寸搜索打印记录
  - 软删除打印记录
  - 查看打印记录详情

- **数据展示**
  - 模板名称
  - 打印尺寸（宽x高）
  - 打印份数
  - 打印端（手机iOS、手机安卓、电脑Windows、电脑Mac）
  - 打印时间
  - 模板预览图

- **交互功能**
  - 删除：弹窗确认，软删除记录
  - 查看详情：显示完整的打印记录信息
  - 搜索：支持按名称和尺寸搜索

### 🚧 待实现功能（后续版本）

- **文档打印记录**
  - PDF文档打印记录
  - 默认展示20条数据
  - 按名称搜索

- **图片打印记录**
  - 图片打印记录
  - 默认展示20条数据
  - 按名称搜索

## 🏗️ 技术架构

### 数据库设计

- **主表**: `print_record` - 统一的打印记录表
- **软删除**: 使用 `delete_time` 字段实现软删除
- **索引优化**: 针对用户ID、打印类型、时间等字段建立索引

### 后端架构

- **Model层**: `PrintRecord` - JFinal模型类
- **Service层**: `PrintRecordService` - 业务逻辑处理
- **Controller层**: `PrintRecordController` - API接口控制器
- **工具类**: `PrintRecordKit` - 打印记录工具类

### API接口

- **RESTful设计**: 遵循RESTful API设计规范
- **统一响应格式**: 标准的JSON响应格式
- **错误处理**: 完善的错误码和错误信息

## 📁 文档结构

```
docs/打印记录/
├── README.md                           # 项目概述（本文件）
├── 01_需求分析.md                      # 产品需求分析
├── 02_数据库设计.md                    # 数据库表结构设计
├── 03_API接口设计.md                   # API接口设计规范
├── 04_后端开发任务清单.md              # 开发任务分解
├── 05_后端接口清单.md                  # 后端接口开发清单
├── 06_集成指南.md                      # 功能集成指南
├── 07_API接口文档.md                   # 完整的API接口文档
├── print-record-migration-v1.0.sql    # 数据库迁移脚本
└── 测试数据/                           # 测试相关文件
```

## 🚀 快速开始

### 1. 数据库初始化

执行数据库迁移脚本：

```sql
-- 执行 print-record-migration-v1.0.sql
source docs/打印记录/print-record-migration-v1.0.sql;
```

### 2. 后端部署

确保以下文件已正确部署：

- `src/main/java/com/sandu/xinye/common/model/PrintRecord.java`
- `src/main/java/com/sandu/xinye/common/model/base/BasePrintRecord.java`
- `src/main/java/com/sandu/xinye/api/v2/printrecord/PrintRecordService.java`
- `src/main/java/com/sandu/xinye/api/v2/printrecord/PrintRecordController.java`
- `src/main/java/com/sandu/xinye/common/kit/PrintRecordKit.java`

### 3. 路由配置

确认路由已在 `ApiRoutes.java` 中注册：

```java
// 打印记录
this.add("/api/v2/printRecord", PrintRecordController.class);
```

### 4. 前端集成

在前端打印操作中调用记录接口：

```javascript
// 记录打印行为
fetch('/api/v2/templet/recordPrint', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    templateId: 123,
    templateName: "商品标签",
    printCopies: 5,
    printPlatform: 1
  })
});
```

## 📊 API接口概览

| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 获取模板打印记录列表 | GET | `/api/v2/printRecord/getTemplateList` | 分页查询 |
| 搜索模板打印记录 | GET | `/api/v2/printRecord/searchTemplate` | 按条件搜索 |
| 删除打印记录 | POST | `/api/v2/printRecord/delete` | 软删除 |
| 获取打印记录详情 | GET | `/api/v2/printRecord/detail/{id}` | 查看详情 |
| 保存模板打印记录 | POST | `/api/v2/printRecord/saveTemplate` | 保存记录 |
| 记录模板打印 | POST | `/api/v2/templet/recordPrint` | 集成接口 |

## 🔧 开发说明

### 常量定义

```java
// 打印类型
public static final int PRINT_RECORD_TYPE_TEMPLATE = 1;  // 模板打印
public static final int PRINT_RECORD_TYPE_DOCUMENT = 2;  // 文档打印
public static final int PRINT_RECORD_TYPE_IMAGE = 3;     // 图片打印

// 打印端类型
public static final int PRINT_PLATFORM_IOS = 1;         // 手机iOS
public static final int PRINT_PLATFORM_ANDROID = 2;     // 手机安卓
public static final int PRINT_PLATFORM_WINDOWS = 3;     // 电脑Windows
public static final int PRINT_PLATFORM_MAC = 4;         // 电脑Mac
```

### 工具类使用

```java
// 异步保存打印记录（推荐）
PrintRecordKit.saveTemplatePrintRecordAsync(userId, templet, printCopies, printPlatform);

// 同步保存打印记录
PrintRecordKit.saveTemplatePrintRecord(userId, templet, printCopies, printPlatform);

// 检测打印端类型
Integer platform = PrintRecordKit.detectPlatformFromUserAgent(userAgent);
```

## 🧪 测试

### 单元测试

- 测试PrintRecordService的各个方法
- 测试PrintRecordController的接口响应
- 测试PrintRecordKit工具类的功能

### 集成测试

- 测试完整的打印记录流程
- 测试与现有打印功能的集成
- 测试API接口的正确性

### 性能测试

- 大数据量下的查询性能
- 并发访问测试
- 内存使用情况

## 📈 版本历史

### v1.0 (2024-07-28)

- ✅ 实现模板打印记录功能
- ✅ 完成数据库设计和API接口
- ✅ 提供完整的文档和集成指南

### 后续版本计划

- 🔄 文档打印记录功能
- 🔄 图片打印记录功能
- 🔄 数据统计和分析功能
- 🔄 批量操作功能

## 🤝 贡献指南

1. 遵循现有的代码规范
2. 添加适当的注释和文档
3. 编写单元测试
4. 更新相关文档

## 📞 技术支持

如有问题，请联系XPrinter开发团队。
