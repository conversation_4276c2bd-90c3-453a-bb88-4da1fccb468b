package com.sandu.xinye.common.service;

import com.jfinal.kit.JsonKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.WechatPaySignKit;
import com.sandu.xinye.common.model.VipOrder;

import java.security.interfaces.RSAPrivateKey;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付服务
 * 提供APP场景下单、签名生成等功能
 */
public class WechatPayService {
    
    public static final WechatPayService me = new WechatPayService();
    
    private RSAPrivateKey privateKey;
    
    /**
     * 初始化私钥
     */
    public void init() {
        try {
            WechatPayConfig config = WechatPayConfig.me;
            if (!config.isConfigValid()) {
                LogKit.warn("微信支付配置不完整，跳过初始化");
                return;
            }
            
            String privateKeyPath = config.getPrivateKeyPath();
            if (StrKit.notBlank(privateKeyPath)) {
                this.privateKey = WechatPaySignKit.loadPrivateKey(privateKeyPath);
                LogKit.info("微信支付私钥加载成功");
            }
            
        } catch (Exception e) {
            LogKit.error("微信支付服务初始化失败", e);
        }
    }
    
    /**
     * 创建APP支付订单
     */
    public Map<String, Object> createAppOrder(VipOrder vipOrder) {
        try {
            WechatPayConfig config = WechatPayConfig.me;
            
            // 检查配置
            if (!config.isConfigValid() || privateKey == null) {
                throw new RuntimeException("微信支付配置不完整或私钥未加载");
            }
            
            // 构建下单请求体
            Map<String, Object> orderRequest = buildAppOrderRequest(vipOrder, config);
            String requestBody = JsonKit.toJson(orderRequest);
            
            // 生成签名参数
            String timestamp = WechatPaySignKit.generateTimestamp();
            String nonce = WechatPaySignKit.generateNonce();
            String url = "/v3/pay/transactions/app";
            
            String signature = WechatPaySignKit.sign("POST", url, timestamp, nonce, requestBody, privateKey);
            
            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", buildAuthorizationHeader(config.getMchId(), config.getSerialNo(), timestamp, nonce, signature));
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            
            // TODO: 发送HTTP请求到微信支付API
            // 这里需要实现HTTP客户端调用
            String responseBody = sendHttpRequest(config.getAppOrderUrl(), "POST", headers, requestBody);
            
            // 解析响应
            Map<String, Object> response = JsonKit.parse(responseBody, Map.class);
            String prepayId = (String) response.get("prepay_id");
            
            if (StrKit.isBlank(prepayId)) {
                throw new RuntimeException("微信支付下单失败，未获取到prepay_id");
            }
            
            // 生成APP调起支付参数
            return buildAppPayParams(config.getAppId(), prepayId);
            
        } catch (Exception e) {
            LogKit.error("创建微信支付APP订单失败", e);
            throw new RuntimeException("创建微信支付订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建APP下单请求体
     */
    private Map<String, Object> buildAppOrderRequest(VipOrder vipOrder, WechatPayConfig config) {
        Map<String, Object> request = new HashMap<>();
        request.put("appid", config.getAppId());
        request.put("mchid", config.getMchId());
        request.put("description", getOrderDescription(vipOrder.getPlan()));
        request.put("out_trade_no", vipOrder.getOrderNo());
        request.put("notify_url", config.getNotifyUrl());
        
        // 订单金额
        Map<String, Object> amount = new HashMap<>();
        amount.put("total", vipOrder.getAmount());
        amount.put("currency", "CNY");
        request.put("amount", amount);
        
        return request;
    }
    
    /**
     * 构建Authorization请求头
     */
    private String buildAuthorizationHeader(String mchId, String serialNo, String timestamp, String nonce, String signature) {
        return String.format("WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",timestamp=\"%s\",serial_no=\"%s\",signature=\"%s\"",
            mchId, nonce, timestamp, serialNo, signature);
    }
    
    /**
     * 构建APP调起支付参数
     */
    private Map<String, Object> buildAppPayParams(String appId, String prepayId) {
        String timestamp = WechatPaySignKit.generateTimestamp();
        String nonce = WechatPaySignKit.generateNonce();
        
        // 构建签名串
        String signStr = appId + "\n" + timestamp + "\n" + nonce + "\n" + prepayId + "\n";
        String paySign = WechatPaySignKit.sign("", "", timestamp, nonce, signStr, privateKey);
        
        Map<String, Object> payParams = new HashMap<>();
        payParams.put("appid", appId);
        payParams.put("partnerid", WechatPayConfig.me.getMchId());
        payParams.put("prepayid", prepayId);
        payParams.put("package", "Sign=WXPay");
        payParams.put("noncestr", nonce);
        payParams.put("timestamp", timestamp);
        payParams.put("sign", paySign);
        
        return payParams;
    }
    
    /**
     * 获取订单描述
     */
    private String getOrderDescription(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return "XPrinter VIP月度会员";
            case VipOrder.PLAN_YEARLY:
                return "XPrinter VIP年度会员";
            default:
                return "XPrinter VIP会员";
        }
    }
    
    /**
     * 发送HTTP请求（简化实现，实际需要使用HTTP客户端）
     */
    private String sendHttpRequest(String url, String method, Map<String, String> headers, String body) {
        // TODO: 实现HTTP客户端调用
        // 可以使用 Apache HttpClient 或 OkHttp
        LogKit.info("发送微信支付请求: " + url);
        LogKit.debug("请求体: " + body);
        
        // 临时返回模拟响应
        return "{\"prepay_id\":\"wx123456789\"}";
    }
    
    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return WechatPayConfig.me.isConfigValid() && privateKey != null;
    }
}
