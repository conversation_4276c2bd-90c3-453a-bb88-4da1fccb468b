# 推广码功能实施计划

## 1. 项目概述

### 1.1 项目目标
实现用户推广码绑定功能，支持用户在注册时或注册后绑定代理商推广码，为后续的分佣体系提供基础数据支持。

### 1.2 项目范围
- ✅ 用户表扩展（添加推广码字段）
- ✅ 注册接口改造（支持推广码参数）
- ✅ 推广码绑定接口开发
- ❌ 代理商管理（第三方系统负责）
- ❌ 分佣计算（第三方系统负责）

### 1.3 技术栈
- 后端框架：JFinal
- 数据库：MySQL
- 开发语言：Java 8+

## 2. 详细实施计划

### 2.1 第一阶段：数据库设计与实施（1天）

#### 2.1.1 任务清单
- [x] 数据库表结构设计
- [ ] 编写数据库迁移脚本
- [ ] 执行数据库变更
- [ ] 验证字段添加成功
- [ ] 更新BaseUser模型类

#### 2.1.2 具体步骤
```sql
-- 1. 执行数据库迁移脚本
ALTER TABLE `user` ADD COLUMN `promotion_code` VARCHAR(50) DEFAULT NULL COMMENT '绑定的推广码';
ALTER TABLE `user` ADD COLUMN `promotion_bind_time` DATETIME DEFAULT NULL COMMENT '推广码绑定时间';

-- 2. 添加索引
CREATE INDEX `idx_promotion_code` ON `user` (`promotion_code`);
CREATE INDEX `idx_promotion_bind_time` ON `user` (`promotion_bind_time`);

-- 3. 验证变更
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user' 
AND COLUMN_NAME IN ('promotion_code', 'promotion_bind_time');
```

#### 2.1.3 预期产出
- 用户表成功添加推广码相关字段
- 索引创建完成，查询性能优化
- BaseUser模型类自动更新

### 2.2 第二阶段：注册接口改造（2天）

#### 2.2.1 任务清单
- [ ] 修改UserLoginController.register()方法
- [ ] 修改UserLoginService.register()方法
- [ ] 修改验证码登录接口
- [ ] 修改第三方登录接口
- [ ] 添加推广码验证工具类
- [ ] 编写单元测试

#### 2.2.2 具体步骤

**步骤1：创建推广码验证工具类**
```java
// 文件：src/main/java/com/sandu/xinye/common/kit/PromotionCodeKit.java
public class PromotionCodeKit {
    private static final String PROMOTION_CODE_PATTERN = "^[A-Za-z0-9]{6,8}$";
    
    public static boolean isValidFormat(String promotionCode) {
        // 验证推广码格式
    }
    
    public static String normalize(String promotionCode) {
        // 标准化推广码
    }
}
```

**步骤2：修改注册接口**
```java
// 文件：src/main/java/com/sandu/xinye/api/login/UserLoginController.java
@Clear
public void register() {
    String phone = getPara("phone");
    String password = getPara("password");
    String captcha = getPara("captcha");
    String promotionCode = getPara("promotionCode"); // 新增
    
    RetKit ret = UserLoginService.me.register(phone, password, captcha, promotionCode, getIpAddress());
    renderJson(ret);
}
```

**步骤3：修改注册服务**
```java
// 文件：src/main/java/com/sandu/xinye/api/login/UserLoginService.java
public RetKit register(String phone, String password, String captcha, String promotionCode, String ipAddress) {
    // 现有注册逻辑
    // 新增推广码绑定逻辑
}
```

#### 2.2.3 预期产出
- 所有注册接口支持推广码参数
- 推广码验证逻辑完善
- 单元测试覆盖率达到80%以上

### 2.3 第三阶段：推广码绑定接口开发（2天）

#### 2.3.1 任务清单
- [ ] 创建UserController（如果不存在）
- [ ] 实现bindPromotionCode接口
- [ ] 实现getPromotionCodeStatus接口
- [ ] 添加用户服务层方法
- [ ] 编写接口测试
- [ ] 编写API文档

#### 2.3.2 具体步骤

**步骤1：创建用户控制器**
```java
// 文件：src/main/java/com/sandu/xinye/api/user/UserController.java
public class UserController extends AppController {
    
    public void bindPromotionCode() {
        // 推广码绑定接口实现
    }
    
    public void getPromotionCodeStatus() {
        // 推广码状态查询接口实现
    }
}
```

**步骤2：实现用户服务**
```java
// 文件：src/main/java/com/sandu/xinye/api/user/UserService.java
public class UserService {
    
    public RetKit bindPromotionCode(Integer userId, String promotionCode) {
        // 推广码绑定业务逻辑
    }
    
    public RetKit getPromotionCodeStatus(Integer userId) {
        // 推广码状态查询业务逻辑
    }
}
```

**步骤3：添加路由配置**
```java
// 文件：src/main/java/com/sandu/xinye/common/routes/ApiRoutes.java
this.add("/api/user", UserController.class);
```

#### 2.3.3 预期产出
- 推广码绑定接口完成开发
- 推广码状态查询接口完成开发
- 接口测试通过
- API文档更新

### 2.4 第四阶段：测试与优化（1天）

#### 2.4.1 任务清单
- [ ] 单元测试编写与执行
- [ ] 集成测试编写与执行
- [ ] 接口性能测试
- [ ] 代码审查
- [ ] 文档完善

#### 2.4.2 测试用例设计

**功能测试用例：**
1. 用户注册时绑定有效推广码
2. 用户注册时绑定无效推广码
3. 用户注册时不传推广码
4. 已注册用户绑定推广码
5. 已绑定用户重复绑定推广码
6. 推广码格式验证测试

**性能测试用例：**
1. 注册接口响应时间测试
2. 推广码绑定接口响应时间测试
3. 数据库查询性能测试

#### 2.4.3 预期产出
- 所有测试用例通过
- 接口响应时间符合要求
- 代码质量达标
- 文档完整准确

## 3. 风险评估与应对

### 3.1 技术风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 数据库迁移失败 | 中 | 功能无法使用 | 提前备份，准备回滚脚本 |
| 现有接口兼容性 | 低 | 现有功能受影响 | 充分测试，保持向后兼容 |
| 性能影响 | 低 | 系统响应变慢 | 添加索引，优化查询 |

### 3.2 业务风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| 推广码重复绑定 | 中 | 分佣数据错误 | 业务逻辑防重复 |
| 推广码格式不统一 | 低 | 第三方对接困难 | 制定统一格式规范 |
| 用户体验问题 | 低 | 用户投诉 | 充分的错误提示 |

## 4. 部署计划

### 4.1 部署环境
- 开发环境：本地开发测试
- 测试环境：功能测试和集成测试
- 生产环境：正式上线

### 4.2 部署步骤

#### 4.2.1 开发环境部署
1. 执行数据库迁移脚本
2. 部署代码更新
3. 执行单元测试
4. 功能验证

#### 4.2.2 测试环境部署
1. 同步生产数据到测试环境
2. 执行数据库迁移脚本
3. 部署代码更新
4. 执行集成测试
5. 性能测试

#### 4.2.3 生产环境部署
1. 数据库备份
2. 执行数据库迁移脚本
3. 部署代码更新
4. 功能验证
5. 监控系统状态

### 4.3 回滚计划
如果部署出现问题，按以下步骤回滚：
1. 停止应用服务
2. 恢复代码到上一版本
3. 执行数据库回滚脚本
4. 重启应用服务
5. 验证系统功能

## 5. 质量保证

### 5.1 代码质量
- 代码审查：所有代码变更需要经过审查
- 单元测试：测试覆盖率不低于80%
- 代码规范：遵循项目编码规范

### 5.2 测试质量
- 功能测试：覆盖所有业务场景
- 性能测试：确保接口响应时间符合要求
- 兼容性测试：确保不影响现有功能

### 5.3 文档质量
- 技术文档：完整的设计和实现文档
- API文档：详细的接口说明文档
- 用户文档：简洁的使用说明

## 6. 项目里程碑

| 里程碑 | 计划完成时间 | 交付物 |
|--------|--------------|--------|
| 数据库设计完成 | 第1天 | 数据库迁移脚本、字段验证 |
| 注册接口改造完成 | 第3天 | 修改后的注册接口、单元测试 |
| 绑定接口开发完成 | 第5天 | 推广码绑定接口、API文档 |
| 测试与优化完成 | 第6天 | 测试报告、性能报告 |
| 项目上线 | 第7天 | 生产环境部署、功能验证 |

## 7. 资源需求

### 7.1 人力资源
- 后端开发工程师：1人，6天
- 测试工程师：1人，2天
- 数据库管理员：1人，1天

### 7.2 技术资源
- 开发环境：本地开发环境
- 测试环境：独立测试服务器
- 数据库：MySQL 5.7+

## 8. 成功标准

### 8.1 功能标准
- ✅ 用户可以在注册时绑定推广码
- ✅ 已注册用户可以绑定推广码
- ✅ 防止重复绑定推广码
- ✅ 推广码格式验证正确

### 8.2 性能标准
- 注册接口响应时间 < 2秒
- 推广码绑定接口响应时间 < 1秒
- 数据库查询响应时间 < 500ms

### 8.3 质量标准
- 单元测试覆盖率 ≥ 80%
- 代码审查通过率 = 100%
- 生产环境零故障运行
