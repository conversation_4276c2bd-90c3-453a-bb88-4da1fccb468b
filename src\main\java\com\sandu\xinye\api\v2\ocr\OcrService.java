package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.ImageKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import com.sandu.xinye.api.v2.ocr.util.ImageUtils;

import java.awt.image.BufferedImage;
import java.io.File;
import javax.imageio.ImageIO;

/**
 * OCR识图服务类
 * 处理图片识别的核心业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class OcrService {
    
    public static final OcrService me = new OcrService();
    
    // 支持的图片格式
    private static final String[] SUPPORTED_FORMATS = {"jpg", "jpeg", "png", "webp", "bmp"};
    
    // 图片大小限制 (10MB)
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;
    
    // 图片最小尺寸
    private static final int MIN_WIDTH = 100;
    private static final int MIN_HEIGHT = 100;
    
    /**
     * 识别图片中的元素
     * 
     * @param imageFile 上传的图片文件
     * @param imageWidth 图片宽度(可选)
     * @param imageHeight 图片高度(可选)
     * @return 识别结果
     */
    public RetKit recognizeImage(UploadFile imageFile, Integer imageWidth, Integer imageHeight) {
        try {
            // 1. 验证图片文件
            RetKit validationResult = validateImageFile(imageFile);
            if (!validationResult.isTrue("success")) {
                return validationResult;
            }
            
            // 2. 获取图片信息
            BufferedImage image = ImageIO.read(imageFile.getFile());
            if (image == null) {
                return RetKit.fail("无法读取图片文件，请检查图片格式");
            }
            
            int actualWidth = image.getWidth();
            int actualHeight = image.getHeight();
            
            // 使用实际尺寸或传入的尺寸
            int finalWidth = imageWidth != null ? imageWidth : actualWidth;
            int finalHeight = imageHeight != null ? imageHeight : actualHeight;
            
            LogKit.info("图片尺寸: " + actualWidth + "x" + actualHeight + 
                       ", 使用尺寸: " + finalWidth + "x" + finalHeight);
            
            // 3. 调用TextIn API进行识别
            LogKit.info("开始调用TextIn API进行图片识别");
            TextInResponse textInResponse = TextInApiClient.me.recognizeImage(imageFile.getFile());

            if (textInResponse == null) {
                return RetKit.fail("TextIn API调用失败，未返回识别结果");
            }

            // 4. 转换识别结果为XPrinter格式
            LogKit.info("开始转换识别结果为XPrinter格式");
            OcrResponse response = ElementConverter.me.convertToXPrinterFormat(
                textInResponse, imageFile.getFile(), finalWidth, finalHeight);

            // 5. 返回响应数据
            return RetKit.ok("data", response).setMsg("识别成功");
            
        } catch (Exception e) {
            LogKit.error("图片识别处理异常: " + e.getMessage(), e);
            return RetKit.fail("图片识别失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (imageFile != null && imageFile.getFile() != null) {
                imageFile.getFile().delete();
            }
        }
    }
    
    /**
     * 验证图片文件
     */
    private RetKit validateImageFile(UploadFile imageFile) {
        if (imageFile == null) {
            return RetKit.fail("请上传图片文件");
        }
        
        File file = imageFile.getFile();
        if (file == null || !file.exists()) {
            return RetKit.fail("图片文件不存在");
        }
        
        // 检查文件大小
        if (file.length() > MAX_FILE_SIZE) {
            return RetKit.fail("图片文件过大，最大支持10MB");
        }
        
        if (file.length() == 0) {
            return RetKit.fail("图片文件为空");
        }
        
        // 检查文件格式
        String fileName = imageFile.getFileName();
        if (StrKit.isBlank(fileName)) {
            return RetKit.fail("图片文件名不能为空");
        }
        
        if (!ImageKit.isImageExtName(fileName)) {
            return RetKit.fail("不支持的图片格式，请上传jpg、png、webp等格式的图片");
        }
        
        return RetKit.ok();
    }
    
    /**
     * 获取图片格式
     */
    private String getImageFormat(String fileName) {
        if (StrKit.isBlank(fileName)) {
            return "unknown";
        }
        
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot + 1).toLowerCase();
        }
        
        return "unknown";
    }
}
