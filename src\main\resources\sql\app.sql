#sql("font.paginate")
   select f.*, fkl.fontLang locale from font f
   left join font_kind_lang fkl on fkl.fontKind = f.fontKind
    where 1 = 1
    #if(sk.notBlank(fontKind))
        and f.fontKind = #p(fontKind)
        #end
    order by f.createTime desc
#end


#sql("machine.list")
	select machineId,machineName from machine
	where 1=1
	#if(sk.notBlank(name))
		and machineName like #p(name)
	#end
#end

#sql("templet.paginate")
    select t.*, ifnull(t.machineType, 0) AS machineType
    from (
             SELECT t.*
             FROM templet t
             where userId = #p(userId) and deleteTime IS NULL
             union
             (
                 SELECT tt.*
                 FROM templet tt
                          inner join user uu on uu.userId = tt.userId and uu.wxUnionId is not null
                          left join user u on uu.wxUnionId = u.wxUnionId
                 where u.userId = #p(userId) and tt.deleteTime IS NULL
             )
         ) t
    where type != 1
      -- 过滤掉已删除分组中的模板：只显示未分类模板(-1)或存在且未删除的分组中的模板
      and (t.groupId = -1 OR EXISTS (
          SELECT 1 FROM templet_group tg 
          WHERE tg.id = t.groupId 
            AND tg.userId = #p(userId) 
            AND tg.deleteTime IS NULL
      ))
	#if(sk.notBlank(groupId))
        and groupId = #p(groupId)
    #end
	#if(sk.notBlank(name))
		and name like #p(name)
	#end
    #if(sk.notBlank(widthBegin))
        and width > #p(widthBegin)
    #end
    #if(sk.notBlank(widthEnd))
        and width <= #p(widthEnd)
    #end
	order by updateTime desc
#end

#sql("templet.share.paginate")
select t.*, ifnull(machineType, 0) as machineType from templet t
where  type != 1 and deleteTime IS NULL
	#if(sk.notNull(shareUser))
        and shareUser = #p(shareUser)
    #end
    #if(shareUser == null)
        and userId = #p(userId) and shareUser is not null
    #end
order by id desc
#end

#sql("templet.busi.paginate")
    select t.*, ifnull(machineType, 0) as machineType from templet t
	where type = 1 and deleteTime IS NULL
	#if(sk.notBlank(groupId))
    	and groupId = #p(groupId)
    #end
	#if(sk.notBlank(name))
		and name like #p(name)
	#end
	#if(sk.notBlank(widthBegin))
        and width > #p(widthBegin)
    #end
    #if(sk.notBlank(widthEnd))
        and width <= #p(widthEnd)
    #end
	order by id desc
#end

#sql("logo.paginate")
SELECT g.logoId, g.logoImg,
#if(lang == 1)
    k.logoKindName as logoKindName
#else if(lang == 2)
    k.logoKindName as logoKindName
#else if(lang == 3)
    k.traditionalName as logoKindName
#else if(lang == 4)
    k.koreanName as logoKindName
#else
    k.logoKindName as logoKindName
#end
FROM logo g
    inner join logo_kind k on k.logoKindId = g.logoKindId
    where g.version = 2
        #if(sk.notBlank(kindId))
            and g.logoKindId = #p(kindId)
        #end
#end

#sql("templet.share.batchSave")
insert into templet (id, name, width, height, type, groupId, userId, shareUser, createTime, updateTime, machineType)
select id, name, width, height, type, groupId, userId, shareUser, createTime, updateTime, machineType from templet where id in (#{ids})

#end


#sql("goods.templet.paginate")
    select t.*, ifnull(t.machineType, 0) AS machineType
    FROM goods_templet t
    where (userId = 0 or userId = #p(userId))
	#if(sk.notBlank(name))
		and name like #p(name)
	#end
    #if(sk.notBlank(widthBegin))
        and width > #p(widthBegin)
    #end
    #if(sk.notBlank(widthEnd))
        and width <= #p(widthEnd)
    #end
	order by sourceType asc, updateTime desc
#end

#sql("printRecord.template.list")
    select * from print_record
    where userId = #p(userId) and printType = #p(printType) and deleteTime IS NULL
    order by printTime desc
#end

#sql("printRecord.template.search")
    select * from print_record
    where userId = #p(userId) and printType = #p(printType) and deleteTime IS NULL
    #if(sk.notBlank(keyword))
        and sourceName like #p(keyword)
    #end
    #if(sk.notNull(width))
        and printWidth = #p(width)
    #end
    #if(sk.notNull(height))
        and printHeight = #p(height)
    #end
    order by printTime desc
#end

#sql("printRecord.detail")
    select * from print_record
    where id = #p(id) and deleteTime IS NULL
#end
