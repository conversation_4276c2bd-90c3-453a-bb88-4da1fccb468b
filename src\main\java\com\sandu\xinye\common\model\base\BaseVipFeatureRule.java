package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseVipFeatureRule<M extends BaseVipFeatureRule<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setMethod(java.lang.String method) {
		set("method", method);
		return (M)this;
	}
	
	public java.lang.String getMethod() {
		return getStr("method");
	}

	public M setPattern(java.lang.String pattern) {
		set("pattern", pattern);
		return (M)this;
	}
	
	public java.lang.String getPattern() {
		return getStr("pattern");
	}

	public M setPermission(java.lang.String permission) {
		set("permission", permission);
		return (M)this;
	}
	
	public java.lang.String getPermission() {
		return getStr("permission");
	}

	public M setEnabled(java.lang.Boolean enabled) {
		set("enabled", enabled);
		return (M)this;
	}
	
	public java.lang.Boolean getEnabled() {
		return get("enabled");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public M setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
		return (M)this;
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
