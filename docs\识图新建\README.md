# XPrinter 识图新建标签功能

## 功能概述
识图新建标签功能允许用户通过拍照上传标签/模板纸图片，后端调用TextIn API进行图像识别，将识别结果转换为APP端可用的JSON格式数据，APP端根据这些数据在编辑界面中渲染相应的组件元素。

## 技术架构

### 整体流程
```
用户拍照 → APP上传 → 后端接收 → TextIn识别 → 数据转换 → 返回结果 → APP渲染
```

### 核心组件
1. **OcrController**: 处理HTTP请求，文件上传验证
2. **OcrService**: 业务逻辑处理，协调各组件
3. **TextInApiClient**: TextIn API调用客户端
4. **ElementConverter**: 数据格式转换器
5. **CoordinateUtils**: 坐标转换工具
6. **ImageUtils**: 图片处理工具

## 支持的元素类型
- **文本元素** (elementType: "1"): 识别文字内容、位置、字体样式
- **条形码** (elementType: "2"): 识别一维码内容和类型
- **二维码** (elementType: "7"): 识别二维码内容
- **图片元素** (elementType: "8"): 识别图片、logo、图标、印章等图形内容
- **表格** (elementType: "10"): 识别表格结构和单元格内容
- **边框** (elementType: "11"): 识别边框元素（1行1列空表格） 🆕

## API接口

### 识图接口
```
POST /api/v2/ocr/recognize
Content-Type: multipart/form-data

参数:
- file: 图片文件 (必填)
- imageWidth: 图片宽度px (可选)
- imageHeight: 图片高度px (可选)
```

### 健康检查接口
```
GET /api/v2/ocr/health
```

## 数据格式

### 输入要求
- **文件格式**: jpg、jpeg、png、webp、bmp
- **文件大小**: 最大10MB
- **图片质量**: 清晰、光线充足、无严重变形

### 输出格式
返回标准的XPrinter元素数据格式，包含：
- 图片信息（尺寸、格式）
- 元素列表（位置、内容、样式等）

## 配置说明

### 必需配置
在`common_config.txt`中添加：
```properties
textin.api.url=https://api.textin.com/ai/service/v1/recognize
textin.api.key=your_textin_api_key
textin.app.id=your_textin_app_id
```

### 依赖包
已添加到`pom.xml`：
- Apache HttpClient (TextIn API调用)
- ZXing (条码识别补充)
- Apache Commons Imaging (图片处理增强)

## 部署指南

### 1. 环境准备
- Java 8+
- Maven 3.6+
- MySQL数据库
- TextIn API账号和密钥

### 2. 配置步骤
1. 获取TextIn API密钥和应用ID
2. 更新`common_config.txt`配置文件
3. 编译项目：`mvn compile`
4. 运行测试：`mvn test`
5. 打包部署：`mvn package`

### 3. 验证部署
1. 访问健康检查接口：`GET /api/v2/ocr/health`
2. 上传测试图片进行识别
3. 检查日志确认功能正常

## 测试说明

### 单元测试
- **OcrServiceTest**: 服务层业务逻辑测试
- **CoordinateUtilsTest**: 坐标转换工具测试
- **ElementConverterTest**: 数据转换器测试
- **ImageElementTest**: 图片元素功能测试 🆕

### 集成测试
参考`docs/识图新建/API测试示例.md`进行完整流程测试

### 测试覆盖
- 文件验证逻辑
- 坐标转换算法
- 数据格式转换
- 错误处理机制

## 性能指标

### 响应时间
- 小图片(< 1MB): 5-8秒
- 中等图片(1-5MB): 8-12秒
- 大图片(5-10MB): 12-20秒

### 并发能力
- 建议并发数: ≤ 10
- 最大文件大小: 10MB
- 超时设置: 60秒

### 准确率
- 文本识别: ≥ 95%
- 条码识别: ≥ 98%
- 二维码识别: ≥ 98%
- 图片识别: ≥ 85% 🆕
- 表格识别: ≥ 90%

## 错误处理

### 常见错误
1. **文件相关**: 格式不支持、文件过大、文件损坏
2. **API相关**: 网络超时、服务不可用、配置错误
3. **数据相关**: 识别失败、转换异常

### 降级策略
- TextIn服务不可用时返回友好错误提示
- 部分识别失败时返回已识别的元素
- 网络超时时提供重试建议

## 监控和日志

### 关键日志
- 请求开始/结束
- TextIn API调用状态
- 识别结果统计
- 错误详情记录

### 监控指标
- 接口调用次数
- 成功/失败率
- 平均响应时间
- 错误类型分布

## 后续优化

### 功能增强
1. 支持更多图片格式
2. 增加图片预处理功能（图片增强、去噪、锐化）
3. 优化表格识别准确率
4. 支持批量图片处理
5. 备用OCR服务集成（TextIn服务不可用时的降级策略）
6. 图片质量自动检测和建议

### 性能优化
1. 实现异步处理机制（长时间识别任务后台处理）
2. 添加结果缓存（相同图片MD5缓存结果）
3. 优化图片压缩算法
4. 支持分布式部署
5. 连接池优化（HTTP连接复用）
6. 识别结果流式返回

### 用户体验
1. 提供识别进度反馈
2. 支持识别结果预览
3. 允许手动调整识别结果
4. 提供识别质量评分
5. 支持识别历史记录
6. 提供识别建议和最佳实践提示

### 安全性优化
1. 图片内容安全检测（敏感信息识别和脱敏）
2. API调用频率限制（防恶意刷接口）
3. 用户上传图片存储加密
4. 识别结果数据脱敏处理
5. 配置信息安全管理（环境变量/配置中心）

### 监控运维
1. 识别成功率监控和告警
2. API响应时间监控
3. TextIn服务可用性监控
4. 成本统计和预警（按用户/按时间统计调用次数）
5. 错误日志分析和自动告警
6. 业务指标Dashboard（识别量趋势、热门功能等）

### 成本控制
1. 智能识别策略（简单图片优先使用本地OCR）
2. 重复图片检测和缓存
3. 用户配额管理
4. 识别结果置信度评估（低置信度结果人工review）

## 联系信息
如有问题或建议，请联系开发团队。

## 更新日志
- **v1.2.0** (2025-08-11): 
  - 修复markdown格式符号清理功能
  - 修复HTML img标签移除功能
  - 优化ZXing条码识别，支持URL图片识别
  - 新增边框元素类型支持（elementType: "11"） 🆕
- **v1.1.0** (2025-08-08): 新增图片元素识别功能，支持logo、图标、印章等图形识别
- **v1.0.0** (2025-08-01): 初始版本，支持基本的图片识别功能
