# 权限控制与识图接入

## 识图新建（OCR）权限
- 接口位置：src/main/java/com/sandu/xinye/api/v2/ocr/OcrController.java#recognize
- 推荐方案：使用全局拦截器 + 数据库路由映射进行 VIP 权限校验，避免在控制器中逐个接口手写判断（解耦、可配置）。

拦截器伪码：
```java
public class VipFeatureInterceptor implements Interceptor {
    public void intercept(Invocation inv) {
        HttpServletRequest req = inv.getController().getRequest();
        String method = req.getMethod();
        String path = req.getRequestURI();
        // 1) 从 DB/缓存 查询匹配的 feature（如 FEATURE_OCR_CREATE）
        FeatureRule rule = VipFeatureRuleService.me.match(method, path);
        if (rule == null || !rule.isEnabled()) { inv.invoke(); return; }
        // 2) 需要登录？（与 AppUserInterceptor 配合，或在此检查会话）
        Integer userId = inv.getController().getAttr("userId");
        if (userId == null) { renderNeedLogin(); return; }
        // 3) 校验权限
        boolean allowed = VipPermissionService.me.hasPermission(userId, rule.getPermissionType());
        if (!allowed) { renderNeedVip(); return; }
        inv.invoke();
    }
}
```

- 新增路由映射（DB 配置），如：
  - method=POST, pattern=/api/v2/ocr/recognize, permission=FEATURE_OCR_CREATE, enabled=true
- 上线新 VIP 功能仅需插入配置，不改控制器代码

## 错误码与引导
- 可在 RetKit 里约定业务码 e.g. code = 40301（需要VIP）
- 返回字段：needVip=true, upgradeUrl=/vip/purchase

## 后续扩展
- 配额控制（QUOTA_OCR_MONTHLY）
- 体验开关（体验期白名单）

