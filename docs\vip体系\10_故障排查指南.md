# VIP体系故障排查指南

## 常见问题与解决方案

### 1. VIP权限拦截不生效

**症状**：非VIP用户可以正常访问OCR等VIP功能

**排查步骤**：
1. 检查 vip_feature_rule 表是否有对应规则
```sql
SELECT * FROM vip_feature_rule WHERE pattern LIKE '%ocr%';
```

2. 检查 VipFeatureInterceptor 是否正确注册
```java
// 在 v2/ApiRoutes.java 中确认
this.addInterceptor(new VipFeatureInterceptor());
```

3. 检查日志中是否有拦截器执行记录
```
grep "VIP权限检查" logs/app.log
```

**解决方案**：
- 确保规则表中有正确的记录
- 确认拦截器注册顺序（应在 AppUserInterceptor 之后）
- 重启应用清除缓存

### 2. 微信支付下单失败

**症状**：创建订单时返回"创建微信支付订单失败"

**排查步骤**：
1. 检查微信支付配置
```java
WechatPayConfig.me.printConfigStatus();
```

2. 检查私钥文件是否存在且可读
```bash
ls -la certs/wechat_pay_private_key.pem
```

3. 检查网络连接
```bash
curl -I https://api.mch.weixin.qq.com
```

**解决方案**：
- 完善微信支付配置
- 确保私钥文件路径正确
- 检查防火墙和网络设置

### 3. 支付回调验签失败

**症状**：微信支付成功但订单状态未更新

**排查步骤**：
1. 检查回调日志
```sql
SELECT * FROM vip_pay_notify_log ORDER BY create_time DESC LIMIT 10;
```

2. 检查微信支付平台证书
```
grep "微信支付平台证书" logs/app.log
```

3. 验证回调URL是否可访问
```bash
curl -X POST https://your-domain.com/api/v2/vip/pay/wechat/notify
```

**解决方案**：
- 实现微信支付平台证书下载和缓存
- 确保回调URL外网可访问且为HTTPS
- 检查API v3密钥是否正确

### 4. VIP开通失败

**症状**：支付成功但用户VIP状态未更新

**排查步骤**：
1. 检查发货日志
```sql
SELECT * FROM vip_delivery_log WHERE order_no = 'VIP123456789';
```

2. 检查用户表更新
```sql
SELECT userId, userTier, vipExpireTime FROM user WHERE userId = 123;
```

3. 检查事务是否回滚
```
grep "VIP发货" logs/app.log
```

**解决方案**：
- 检查数据库事务配置
- 确认用户ID和订单关联正确
- 手动执行发货逻辑进行修复

### 5. 订单重复创建

**症状**：用户可以创建多个未支付订单

**排查步骤**：
1. 检查订单创建逻辑
```java
VipOrder existingOrder = findUnpaidOrder(userId);
```

2. 查看用户的未支付订单
```sql
SELECT * FROM vip_order WHERE user_id = 123 AND status = 'created';
```

**解决方案**：
- 确保订单创建前检查未支付订单
- 添加数据库唯一约束
- 实现订单自动过期机制

## 性能问题排查

### 1. VIP权限检查慢

**排查**：
- 检查 vip_feature_rule 表索引
- 监控缓存命中率
- 分析SQL执行计划

**优化**：
- 添加适当索引
- 启用规则缓存
- 优化规则匹配算法

### 2. 支付回调处理慢

**排查**：
- 监控回调处理时间
- 检查数据库连接池
- 分析慢查询日志

**优化**：
- 异步处理非关键逻辑
- 优化数据库查询
- 增加连接池大小

## 数据一致性问题

### 1. 订单状态不一致

**检查脚本**：
```sql
-- 查找状态异常的订单
SELECT o.*, u.userTier, u.vipExpireTime 
FROM vip_order o 
LEFT JOIN user u ON o.user_id = u.userId 
WHERE o.status = 'paid' AND u.userTier = 'FREE';
```

**修复脚本**：
```sql
-- 手动触发VIP开通（谨慎使用）
UPDATE user SET userTier = 'VIP_MONTHLY', vipExpireTime = DATE_ADD(NOW(), INTERVAL 1 MONTH) 
WHERE userId IN (
    SELECT user_id FROM vip_order 
    WHERE status = 'paid' AND plan = 'monthly'
    AND user_id NOT IN (SELECT userId FROM user WHERE userTier != 'FREE')
);
```

### 2. VIP到期时间错误

**检查脚本**：
```sql
-- 查找到期时间异常的用户
SELECT userId, userTier, vipExpireTime 
FROM user 
WHERE userTier != 'FREE' AND vipExpireTime < NOW();
```

**修复脚本**：
```sql
-- 手动执行过期处理
UPDATE user SET userTier = 'FREE' 
WHERE userTier != 'FREE' AND vipExpireTime < NOW();
```

## 监控指标

### 关键指标
- VIP转化率：`paid_orders / total_users`
- 支付成功率：`paid_orders / created_orders`
- 回调成功率：`success_callbacks / total_callbacks`
- 权限拦截率：`blocked_requests / total_vip_requests`

### 告警规则
- 支付成功率 < 95%
- 回调失败率 > 5%
- VIP功能异常访问 > 100次/小时
- 订单创建失败率 > 10%

## 应急处理

### 1. 紧急关闭VIP功能
```sql
-- 临时禁用所有VIP功能规则
UPDATE vip_feature_rule SET enabled = 0;
```

### 2. 紧急开通VIP
```sql
-- 手动为用户开通VIP（替换用户ID和到期时间）
UPDATE user SET userTier = 'VIP_MONTHLY', vipExpireTime = '2024-12-31 23:59:59' 
WHERE userId = 123;
```

### 3. 订单状态修复
```sql
-- 将异常订单标记为已关闭
UPDATE vip_order SET status = 'closed', update_time = NOW() 
WHERE status = 'created' AND create_time < DATE_SUB(NOW(), INTERVAL 1 DAY);
```

## 日志分析

### 关键日志关键词
- `VIP权限检查` - 权限拦截日志
- `VIP订单创建` - 订单创建日志
- `微信支付回调` - 支付回调日志
- `VIP发货` - VIP开通日志

### 日志分析命令
```bash
# 统计今日VIP订单数
grep "VIP订单创建成功" logs/app.log | grep $(date +%Y-%m-%d) | wc -l

# 查看支付回调失败
grep "微信支付回调.*失败" logs/app.log | tail -20

# 统计权限拦截次数
grep "VIP权限检查.*无权限" logs/app.log | grep $(date +%Y-%m-%d) | wc -l
```

## 联系支持

如果以上方法无法解决问题，请收集以下信息联系技术支持：

1. 错误日志（最近1小时）
2. 相关订单号或用户ID
3. 问题发生的具体时间
4. 用户操作步骤
5. 系统环境信息（数据库版本、JDK版本等）
