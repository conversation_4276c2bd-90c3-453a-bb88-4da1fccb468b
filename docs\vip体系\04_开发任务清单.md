# 开发任务清单（v1）

## A. 数据库与模型
- [ ] A1. user 表新增 vipExpireTime 字段与索引，提供 SQL 脚本
- [ ] A2. 生成 BaseUser 字段访问器与 User 扩展方法（isVip(), buildVipInfo更新带到期时间）
- [ ] A3. 新建 vip_order（及可选 vip_delivery_log）表 SQL
- [ ] A4. 生成 VipOrder 模型与 DAO

## B. 支付与订单
- [ ] B1. 接入微信支付（v3）：商户配置、签名、下单参数构造
- [ ] B2. 新建 VipOrderService：创建订单、查询订单
- [ ] B3. 新建 VipPayService：聚合支付能力 + WechatPayClient（或轻量实现）
- [ ] B4. 新建 VipPayCallbackController：处理微信回调、验签与解密
- [ ] B5. 幂等处理：基于 order_no 唯一与订单状态机
- [ ] B6. VipDeliveryService：根据 plan 计算到期时间并更新用户（叠加规则）

## C. 接口与权限
- [ ] C1. VipOrderController：/vip/order/create, /vip/order/get
- [ ] C2. VipUserController：/vip/status
- [ ] C3. 全局拦截器：VipFeatureInterceptor（基于 DB 配置拦截 VIP 功能），注册到 ApiRoutes
- [ ] C4. VipFeatureRuleService：基于表 vip_feature_rule 提供规则匹配（method + pattern 前缀/通配）与缓存
- [ ] C5. RetKit 统一返回升级引导的错误码/提示文案

## D. 配置与运维
- [ ] D1. application/common_config.txt 增加微信支付参数（mchid、appid、apiV3Key、serialNo、privateKeyPath）
- [ ] D2. 本地/测试环境密钥与证书管理
- [ ] D3. 日志与审计：订单、回调、发货流水

## E. 测试
- [ ] E1. 单元测试：到期时间叠加规则（DeliveryService）
- [ ] E2. 集成测试：模拟回调幂等与验签（可局部mock）
- [ ] E3. 接口测试：下单/查单/状态/OCR拒绝

## 里程碑
- M1：数据库与模型就绪（A）
- M2：订单下单与回调打通（B）
- M3：权限拦截与接口上线（C）
- M4：联调与测试通过（D/E）

