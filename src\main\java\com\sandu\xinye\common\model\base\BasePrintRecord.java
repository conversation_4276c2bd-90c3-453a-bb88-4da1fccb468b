package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BasePrintRecord<M extends BasePrintRecord<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Long id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}

	public M setUserId(java.lang.Integer userId) {
		set("userId", userId);
		return (M)this;
	}
	
	public java.lang.Integer getUserId() {
		return getInt("userId");
	}

	public M setPrintType(java.lang.Integer printType) {
		set("printType", printType);
		return (M)this;
	}
	
	public java.lang.Integer getPrintType() {
		return getInt("printType");
	}

	public M setSourceId(java.lang.Integer sourceId) {
		set("sourceId", sourceId);
		return (M)this;
	}
	
	public java.lang.Integer getSourceId() {
		return getInt("sourceId");
	}

	public M setSourceName(java.lang.String sourceName) {
		set("sourceName", sourceName);
		return (M)this;
	}
	
	public java.lang.String getSourceName() {
		return getStr("sourceName");
	}

	public M setSourceCover(java.lang.String sourceCover) {
		set("sourceCover", sourceCover);
		return (M)this;
	}
	
	public java.lang.String getSourceCover() {
		return getStr("sourceCover");
	}

	public M setPrintWidth(java.lang.Integer printWidth) {
		set("printWidth", printWidth);
		return (M)this;
	}
	
	public java.lang.Integer getPrintWidth() {
		return getInt("printWidth");
	}

	public M setPrintHeight(java.lang.Integer printHeight) {
		set("printHeight", printHeight);
		return (M)this;
	}
	
	public java.lang.Integer getPrintHeight() {
		return getInt("printHeight");
	}

	public M setPrintCopies(java.lang.Integer printCopies) {
		set("printCopies", printCopies);
		return (M)this;
	}
	
	public java.lang.Integer getPrintCopies() {
		return getInt("printCopies");
	}

	public M setPrintPlatform(java.lang.Integer printPlatform) {
		set("printPlatform", printPlatform);
		return (M)this;
	}
	
	public java.lang.Integer getPrintPlatform() {
		return getInt("printPlatform");
	}

	public M setPrintTime(java.util.Date printTime) {
		set("printTime", printTime);
		return (M)this;
	}
	
	public java.util.Date getPrintTime() {
		return get("printTime");
	}

	public M setSourceData(java.lang.String sourceData) {
		set("sourceData", sourceData);
		return (M)this;
	}
	
	public java.lang.String getSourceData() {
		return getStr("sourceData");
	}

	public M setFileType(java.lang.String fileType) {
		set("fileType", fileType);
		return (M)this;
	}
	
	public java.lang.String getFileType() {
		return getStr("fileType");
	}

	public M setFileSize(java.lang.Long fileSize) {
		set("fileSize", fileSize);
		return (M)this;
	}
	
	public java.lang.Long getFileSize() {
		return getLong("fileSize");
	}

	public M setPrintStatus(java.lang.Integer printStatus) {
		set("printStatus", printStatus);
		return (M)this;
	}
	
	public java.lang.Integer getPrintStatus() {
		return getInt("printStatus");
	}

	public M setErrorMessage(java.lang.String errorMessage) {
		set("errorMessage", errorMessage);
		return (M)this;
	}
	
	public java.lang.String getErrorMessage() {
		return getStr("errorMessage");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setUpdateTime(java.util.Date updateTime) {
		set("updateTime", updateTime);
		return (M)this;
	}
	
	public java.util.Date getUpdateTime() {
		return get("updateTime");
	}

	public M setDeleteTime(java.util.Date deleteTime) {
		set("deleteTime", deleteTime);
		return (M)this;
	}
	
	public java.util.Date getDeleteTime() {
		return get("deleteTime");
	}

}
