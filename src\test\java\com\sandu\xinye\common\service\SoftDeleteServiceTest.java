package com.sandu.xinye.common.service;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Templet;
import com.sandu.xinye.common.model.TempletGroup;
import com.sandu.xinye.common.model.TempletHistory;
import org.junit.Before;
import org.junit.Test;
import org.junit.After;

import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;

/**
 * SoftDeleteService 测试类
 * 主要测试删除模板分组时同步删除历史记录的功能
 */
public class SoftDeleteServiceTest {
    
    private SoftDeleteService softDeleteService;
    private Integer testUserId = 999999; // 使用一个测试用户ID
    private Integer testGroupId;
    private Integer testTemplateId1;
    private Integer testTemplateId2;
    
    @Before
    public void setUp() {
        softDeleteService = SoftDeleteService.me;
        
        // 创建测试数据
        createTestData();
    }
    
    @After
    public void tearDown() {
        // 清理测试数据
        cleanupTestData();
    }
    
    /**
     * 创建测试数据：分组、模板、历史记录
     */
    private void createTestData() {
        try {
            // 1. 创建测试分组
            TempletGroup group = new TempletGroup();
            group.setUserId(testUserId)
                 .setName("测试分组_" + System.currentTimeMillis())
                 .setCreateTime(new Date())
                 .setType(1);
            group.save();
            testGroupId = group.getId();
            
            // 2. 创建测试模板1
            Templet template1 = new Templet();
            template1.setUserId(testUserId)
                     .setGroupId(testGroupId)
                     .setName("测试模板1_" + System.currentTimeMillis())
                     .setCreateTime(new Date())
                     .setType(1)
                     .setData("test data 1")
                     .setWidth(100)
                     .setHeight(50)
                     .setGap(2.0f)
                     .setPaperType(1)
                     .setPrintDirection(1)
                     .setMachineType(1);
            template1.save();
            testTemplateId1 = template1.getId();
            
            // 3. 创建测试模板2
            Templet template2 = new Templet();
            template2.setUserId(testUserId)
                     .setGroupId(testGroupId)
                     .setName("测试模板2_" + System.currentTimeMillis())
                     .setCreateTime(new Date())
                     .setType(1)
                     .setData("test data 2")
                     .setWidth(100)
                     .setHeight(50)
                     .setGap(2.0f)
                     .setPaperType(1)
                     .setPrintDirection(1)
                     .setMachineType(1);
            template2.save();
            testTemplateId2 = template2.getId();
            
            // 4. 创建历史记录
            TempletHistory history1 = new TempletHistory();
            history1.setTempletId(testTemplateId1)
                    .setUserId(testUserId)
                    .setGroupId(testGroupId)
                    .setName("测试模板1历史")
                    .setCreateTime(new Date())
                    .setData("test history data 1")
                    .setWidth(100)
                    .setHeight(50)
                    .setGap(2.0f)
                    .setPaperType(1)
                    .setPrintDirection(1)
                    .setType(1);
            history1.save();
            
            TempletHistory history2 = new TempletHistory();
            history2.setTempletId(testTemplateId2)
                    .setUserId(testUserId)
                    .setGroupId(testGroupId)
                    .setName("测试模板2历史")
                    .setCreateTime(new Date())
                    .setData("test history data 2")
                    .setWidth(100)
                    .setHeight(50)
                    .setGap(2.0f)
                    .setPaperType(1)
                    .setPrintDirection(1)
                    .setType(1);
            history2.save();
            
            System.out.println("测试数据创建完成: groupId=" + testGroupId + 
                             ", templateId1=" + testTemplateId1 + 
                             ", templateId2=" + testTemplateId2);
            
        } catch (Exception e) {
            System.err.println("创建测试数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 清理测试数据
     */
    private void cleanupTestData() {
        try {
            // 删除历史记录
            if (testTemplateId1 != null) {
                Db.update("DELETE FROM templet_history WHERE templetId = ?", testTemplateId1);
            }
            if (testTemplateId2 != null) {
                Db.update("DELETE FROM templet_history WHERE templetId = ?", testTemplateId2);
            }
            
            // 删除模板
            if (testTemplateId1 != null) {
                Db.update("DELETE FROM templet WHERE id = ?", testTemplateId1);
            }
            if (testTemplateId2 != null) {
                Db.update("DELETE FROM templet WHERE id = ?", testTemplateId2);
            }
            
            // 删除分组
            if (testGroupId != null) {
                Db.update("DELETE FROM templet_group WHERE id = ?", testGroupId);
            }
            
            System.out.println("测试数据清理完成");
            
        } catch (Exception e) {
            System.err.println("清理测试数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试删除模板分组时同步删除历史记录的功能
     */
    @Test
    public void testSoftDeleteTemplateGroupWithHistoryCleanup() {
        // 验证测试数据存在
        assertNotNull("测试分组ID不能为空", testGroupId);
        assertNotNull("测试模板1 ID不能为空", testTemplateId1);
        assertNotNull("测试模板2 ID不能为空", testTemplateId2);
        
        // 验证删除前历史记录存在
        List<Record> historyBefore = Db.find(
            "SELECT * FROM templet_history WHERE templetId IN (?, ?)", 
            testTemplateId1, testTemplateId2
        );
        assertEquals("删除前应该有2条历史记录", 2, historyBefore.size());
        
        // 执行删除分组操作
        RetKit result = softDeleteService.softDeleteTemplateGroup(testGroupId, testUserId);
        
        // 验证删除结果
        assertTrue("删除分组应该成功", result.success());
        assertEquals("删除成功消息", "分组删除成功", result.getMsg());
        
        // 验证分组被软删除（deleteTime不为空）
        TempletGroup group = TempletGroup.dao.findById(testGroupId);
        assertNotNull("分组应该存在", group);
        assertNotNull("分组应该被软删除", group.get("deleteTime"));
        
        // 验证模板没有被删除（仍然存在且deleteTime为空）
        Templet template1 = Templet.dao.findById(testTemplateId1);
        Templet template2 = Templet.dao.findById(testTemplateId2);
        assertNotNull("模板1应该仍然存在", template1);
        assertNotNull("模板2应该仍然存在", template2);
        assertNull("模板1不应该被软删除", template1.get("deleteTime"));
        assertNull("模板2不应该被软删除", template2.get("deleteTime"));
        
        // 验证历史记录被删除
        List<Record> historyAfter = Db.find(
            "SELECT * FROM templet_history WHERE templetId IN (?, ?)", 
            testTemplateId1, testTemplateId2
        );
        assertEquals("删除后应该没有历史记录", 0, historyAfter.size());
        
        System.out.println("测试通过：删除模板分组成功，历史记录已同步删除，模板保持不变");
    }
    
    /**
     * 测试删除不存在的分组
     */
    @Test
    public void testSoftDeleteNonExistentGroup() {
        RetKit result = softDeleteService.softDeleteTemplateGroup(999999999, testUserId);
        assertFalse("删除不存在的分组应该失败", result.success());
        assertEquals("应该返回分组不存在的错误", "分组不存在", result.getMsg());
    }
    
    /**
     * 测试删除其他用户的分组
     */
    @Test
    public void testSoftDeleteOtherUserGroup() {
        Integer otherUserId = 888888;
        RetKit result = softDeleteService.softDeleteTemplateGroup(testGroupId, otherUserId);
        assertFalse("删除其他用户的分组应该失败", result.success());
        assertEquals("应该返回无权限的错误", "无权限删除此分组", result.getMsg());
    }
}
