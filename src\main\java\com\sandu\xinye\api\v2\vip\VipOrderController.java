package com.sandu.xinye.api.v2.vip;

import com.jfinal.aop.Before;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.AppUserInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.service.VipOrderService;

/**
 * VIP订单控制器
 */
public class VipOrderController extends AppController {
    
    /**
     * 创建VIP订单
     * POST /api/v2/vip/order/create
     * 
     * 参数:
     * - plan: monthly|yearly (必填)
     * - channel: wechat (可选，默认微信)
     */
    @Before({AppUserInterceptor.class})
    public void create() {
        try {
            User user = getUser();
            String plan = getPara("plan");
            String channel = getPara("channel", "wechat");
            
            LogKit.info("用户创建VIP订单: " + user.getUserId() + ", 套餐: " + plan);
            
            // 参数验证
            if (StrKit.isBlank(plan)) {
                renderJson(RetKit.fail("套餐类型不能为空"));
                return;
            }
            
            if (!"monthly".equals(plan) && !"yearly".equals(plan)) {
                renderJson(RetKit.fail("无效的套餐类型，支持: monthly, yearly"));
                return;
            }
            
            if (!"wechat".equals(channel)) {
                renderJson(RetKit.fail("当前仅支持微信支付"));
                return;
            }
            
            // 创建订单
            RetKit result = VipOrderService.me.createOrder(user.getUserId(), plan);
            renderJson(result);
            
        } catch (Exception e) {
            LogKit.error("创建VIP订单失败", e);
            renderJson(RetKit.fail("创建订单失败"));
        }
    }
    
    /**
     * 查询订单详情
     * GET /api/v2/vip/order/get?orderNo=xxx
     */
    @Before({AppUserInterceptor.class})
    public void get() {
        try {
            User user = getUser();
            String orderNo = getPara("orderNo");
            
            if (StrKit.isBlank(orderNo)) {
                renderJson(RetKit.fail("订单号不能为空"));
                return;
            }
            
            RetKit result = VipOrderService.me.getOrder(user.getUserId(), orderNo);
            renderJson(result);
            
        } catch (Exception e) {
            LogKit.error("查询VIP订单失败", e);
            renderJson(RetKit.fail("查询订单失败"));
        }
    }
    
    /**
     * 查询用户订单列表
     * GET /api/v2/vip/order/list?page=1&pageSize=10
     */
    @Before({AppUserInterceptor.class})
    public void list() {
        try {
            User user = getUser();
            int page = getParaToInt("page", 1);
            int pageSize = getParaToInt("pageSize", 10);
            
            RetKit result = VipOrderService.me.getUserOrders(user.getUserId(), page, pageSize);
            renderJson(result);
            
        } catch (Exception e) {
            LogKit.error("查询用户订单列表失败", e);
            renderJson(RetKit.fail("查询失败"));
        }
    }
}
