package com.sandu.xinye.api.v2.login;

import com.jfinal.kit.HashKit;
import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.ehcache.CacheKit;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.constant.Constant;
import com.sandu.xinye.common.kit.AliOpenapiKit;
import com.sandu.xinye.common.kit.AgentPromotionKit;
import com.sandu.xinye.common.kit.PromotionCodeKit;
import com.sandu.xinye.common.kit.RandomKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.SysUserRole;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.UserSession;
import com.sandu.xinye.common.model.UserUnregister;
import com.xiaoleilu.hutool.util.StrUtil;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class UserLoginService {

    public static final UserLoginService me = new UserLoginService();

    private static boolean containsEmoji(String source) {
        int len = source.length();
        boolean isEmoji = false;
        for (int i = 0; i < len; i++) {
            char hs = source.charAt(i);
            if (0xd800 <= hs && hs <= 0xdbff) {
                if (source.length() > 1) {
                    char ls = source.charAt(i + 1);
                    int uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
                    if (0x1d000 <= uc && uc <= 0x1f77f) {
                        return true;
                    }
                }
            } else {
                // non surrogate
                if (0x2100 <= hs && hs <= 0x27ff && hs != 0x263b) {
                    return true;
                } else if (0x2B05 <= hs && hs <= 0x2b07) {
                    return true;
                } else if (0x2934 <= hs && hs <= 0x2935) {
                    return true;
                } else if (0x3297 <= hs && hs <= 0x3299) {
                    return true;
                } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d || hs == 0x3030 || hs == 0x2b55 || hs == 0x2b1c
                        || hs == 0x2b1b || hs == 0x2b50 || hs == 0x231a) {
                    return true;
                }
                if (!isEmoji && source.length() > 1 && i < source.length() - 1) {
                    char ls = source.charAt(i + 1);
                    if (ls == 0x20e3) {
                        return true;
                    }
                }
            }
        }

        // 双重过滤
        Pattern emoji = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]",
                Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
        Matcher emojiMatcher = emoji.matcher(source);
        if (emojiMatcher.find()) {
            isEmoji = true;
        }

        return isEmoji;
    }

    public RetKit register(String phoneOrEmail, String password, String captcha, String promotionCode, String ipAddress) {
        if (StrKit.isBlank(phoneOrEmail) || StrKit.isBlank(password) || StrKit.isBlank(captcha)) {
            return RetKit.fail("账号、密码、验证码不能为空！");
        }
        phoneOrEmail = phoneOrEmail.trim();
        User user = User.dao.findFirst("select * from user where userPhone=?", phoneOrEmail);
        if (user != null) {
            return RetKit.fail("该账号已被注册！");
        }
        String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phoneOrEmail);
        System.out.println(realCaptcha + "验证码");

        if (realCaptcha == null) {
            return RetKit.fail("验证码已过期，请重新发送验证码！");
        }
        if (!captcha.equals(realCaptcha)) {
            return RetKit.fail("验证码错误！");
        }

        // 推广码验证和标准化
        String normalizedPromotionCode = null;
        if (StrKit.notBlank(promotionCode)) {
            if (!PromotionCodeKit.isValidFormat(promotionCode)) {
                return RetKit.fail(PromotionCodeKit.getFormatErrorMessage());
            }
            normalizedPromotionCode = PromotionCodeKit.normalize(promotionCode);
        }

        String userNickName = phoneOrEmail;
        String userImg = "/upload/avatar/img.jpg";
        String salt = HashKit.generateSaltForSha256();
        String hashPwd = HashKit.sha256(salt + password);
        User newUser = new User();

        // 设置用户基本信息和推广码
        newUser.setUserNickName(userNickName).setUserPhone(phoneOrEmail).setUserImg(userImg)
                .setUserPass(hashPwd).setRegisterType(User.REGISTER_TYPE_PHONE).setSalt(salt)
                .setStatus(User.STATUS_IS_REGISTER).setCreateTime(new Date());

        boolean succ = newUser.save();
        if (succ) {
            CacheKit.remove(CacheConstant.CAPTCHA, phoneOrEmail);
            
            // 如果有推广码，在用户创建后验证并绑定
            if (normalizedPromotionCode != null) {
                // 验证推广码在代理商系统中是否存在
                Boolean agentVerifyResult = AgentPromotionKit.verifyPromotionCode(newUser.getUserId(), normalizedPromotionCode);
                if (agentVerifyResult == null) {
                    // 代理商系统验证失败，根据配置决定是否绑定
                    if (AgentPromotionKit.isFailoverEnabled()) {
                        LogKit.warn("用户注册时代理商系统验证失败，允许绑定推广码 - userId: " + newUser.getUserId() + ", promotionCode: " + normalizedPromotionCode);
                        newUser.setPromotionCode(normalizedPromotionCode).setPromotionBindTime(new Date()).update();
                    } else {
                        LogKit.warn("用户注册时代理商系统验证失败，不绑定推广码 - userId: " + newUser.getUserId() + ", promotionCode: " + normalizedPromotionCode);
                        normalizedPromotionCode = null;
                    }
                } else if (!agentVerifyResult) {
                    // 推广码在代理商系统中不存在，不绑定推广码（但不影响用户注册）
                    LogKit.warn("用户注册时推广码不存在，不绑定推广码 - userId: " + newUser.getUserId() + ", promotionCode: " + normalizedPromotionCode);
                    normalizedPromotionCode = null;
                } else {
                    // 验证成功，绑定推广码
                    LogKit.info("用户注册时代理商系统验证推广码成功 - userId: " + newUser.getUserId() + ", promotionCode: " + normalizedPromotionCode);
                    newUser.setPromotionCode(normalizedPromotionCode).setPromotionBindTime(new Date()).update();
                }
            }

            // 构建返回数据，包含推广码绑定状态
            Kv data = Kv.by("userId", newUser.getUserId());
            if (normalizedPromotionCode != null) {
                data.set("promotionCode", normalizedPromotionCode)
                    .set("promotionBound", true);
            } else {
                data.set("promotionCode", null)
                    .set("promotionBound", false);
            }
            
            return RetKit.ok("注册成功").set("data", data);
        }
        return RetKit.fail();
    }

    public RetKit unregister(Integer userId, String accessToken) {
        if (StrKit.isBlank(accessToken)) {
            return RetKit.fail("未登录！");
        }

        UserSession session = UserSession.dao.findById(accessToken);
        if (session == null) {
            return RetKit.fail("未登录或会话已过期！");
        }

        if (!session.getUserId().equals(userId)) {
            return RetKit.fail("错误的用户ID！");
        }

        boolean succ = Db.tx(() -> {
            // Remove everything related to specific userId in the database
            Db.delete("delete from user_login_log where userId = ?", userId);
//            Db.delete("delete from templet where userId = ?", userId);
//            Db.delete("delete from templet_group where userId = ?", userId);
            Db.delete("delete from feedback where userId = ?", userId);
            User user = User.dao.findById(userId);
            UserUnregister unregister = new UserUnregister().setUserId(user.getUserId())
                    .setUserPhone(user.getUserPhone()).setWxId(user.getWxId()).setQqId(user.getQqId()).setWxUnionId(user.getWxUnionId())
                    .setUserNickName(user.getUserNickName()).setRegisterType(user.getRegisterType()).setLastLoginTime(user.getLastLoginTime())
                    .setStatus(user.getStatus()).setUserImg(user.getUserImg())
                    .setUserPass(user.getUserPass()).setSalt(user.getSalt())
                    .setIdentity(user.getIdentity()).setCreateTime(user.getCreateTime())
                    .setUnregisterTime(new Date());
            boolean succ2 = unregister.save();
            if (!succ2) {
                return false;
            }
            User.dao.deleteById(userId);

            // TODO: You must delete the userImg either if they are uploaded as
            // a file.
            return true;
        });
        if (succ) {
            // 清空登录缓存
            removeCacheAndSession(userId);
        }

        return RetKit.ok();
    }

    public RetKit unregister(String phone, String captcha, Integer userId, String accessToken) {
        if (StrKit.isBlank(accessToken)) {
            return RetKit.fail("未登录！");
        }

        if (StrKit.isBlank(phone) || StrKit.isBlank(captcha)) {
            return RetKit.fail("账号和验证码不能为空！");
        }
        phone = phone.trim();
        String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
        System.out.println("unregister:: " + realCaptcha + "验证码");

        if (realCaptcha == null) {
            return RetKit.fail("验证码已过期，请重新发送验证码！");
        }
        if (!captcha.equals(realCaptcha)) {
            return RetKit.fail("验证码错误！");
        }

        UserSession session = UserSession.dao.findById(accessToken);
        if (session == null) {
            return RetKit.fail("未登录或会话已过期！");
        }

        if (!session.getUserId().equals(userId)) {
            return RetKit.fail("错误的用户ID！");
        }

        boolean succ = Db.tx(() -> {
            // Remove everything related to specific userId in the database
            Db.delete("delete from user_login_log where userId = ?", userId);
//            Db.delete("delete from templet where userId = ?", userId);
//            Db.delete("delete from templet_group where userId = ?", userId);
            Db.delete("delete from feedback where userId = ?", userId);
            User user = User.dao.findById(userId);
            UserUnregister unregister = new UserUnregister().setUserId(user.getUserId())
                    .setUserPhone(user.getUserPhone()).setWxId(user.getWxId()).setQqId(user.getQqId()).setWxUnionId(user.getWxUnionId())
                    .setUserNickName(user.getUserNickName()).setRegisterType(user.getRegisterType()).setLastLoginTime(user.getLastLoginTime())
                    .setStatus(user.getStatus()).setUserImg(user.getUserImg())
                    .setUserPass(user.getUserPass()).setSalt(user.getSalt())
                    .setIdentity(user.getIdentity()).setCreateTime(user.getCreateTime())
                    .setUnregisterTime(new Date());
            boolean succ2 = unregister.save();
            if (!succ2) {
                return false;
            }
            User.dao.deleteById(userId);

            // TODO: You must delete the userImg either if they are uploaded as
            // a file.
            return true;
        });
        if (succ) {
            // 清空登录缓存
            removeCacheAndSession(userId);
        }

        return RetKit.ok();
    }

    public RetKit doLogin(String phone, String password, String platform, String ipAddress) {
        if (StrKit.isBlank(phone) || StrKit.isBlank(password)) {
            return RetKit.fail("账号和密码不能为空！");
        }
        phone = phone.trim();
        User user = User.dao.findFirst("select * from user where userPhone=? ", phone);
        if (user == null) {
            return RetKit.fail("该账号未注册！");
        }
        String salt = user.getSalt();
        String hashPwd = HashKit.sha256(salt + password);
        if (hashPwd.equals(user.getUserPass())) {
            // 登录有效时间 7天(秒)
            long liveSeconds = 30 * 24 * 60 * 60;
            // 过期的时间戳
            long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
            // 生成accessToken
            String accessToken = HashKit.sha1(StrKit.getRandomUUID());
            // 创建session
            UserSession session = new UserSession();
            session.setUserId(user.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp)
                    .setCreateTime(new Date()).save();

            // 用户分享码设置
            String identity = user.getIdentity();
            if (StrKit.isBlank(identity)) {
                identity = RandomKit.getRandomCharAndNum(6);
                user.setIdentity(identity);
            }

            // 记录登录时间
            user.setLastLoginTime(new Date()).update();
            // 清楚账号的盐和密码，不暴露出来
            user.removeSensitiveInfo();
            CacheKit.put(CacheConstant.APP_USER, accessToken, user);
            // 创建登录日志
            createLoginLog(user.getUserId(), ipAddress, Integer.valueOf(platform));
            Kv kv = new Kv();
            kv = Kv.by("userId", user.getUserId()).set("userNickName", user.getUserNickName()).set("userPhone", phone)
                    .set("shareCode", user.getIdentity())
                    .set("userImg", user.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken)
                    .set("vipInfo", user.buildVipInfo());
            
            // 添加推广码信息
            kv = addPromotionCodeInfo(kv, user);

            return RetKit.ok("data", kv).setMsg("登录成功");
        } else {
            return RetKit.fail("账号或密码错误");
        }
    }

    /**
     * 验证码登录
     *
     * @param phone
     * @param captcha
     * @param platform
     * @param ipAddress
     * @return
     */
    public RetKit captchaLogin(String phone, String captcha, String promotionCode, String platform, String ipAddress) {
        if (StrKit.isBlank(phone) || StrKit.isBlank(captcha)) {
            return RetKit.fail("账号和验证码不能为空！");
        }
        phone = phone.trim();
        String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
        System.out.println(realCaptcha + "验证码");

        if (realCaptcha == null) {
            return RetKit.fail("验证码已过期，请重新发送验证码！");
        }
        if (!captcha.equals(realCaptcha)) {
            return RetKit.fail("验证码错误！");
        }

        User checkUser = User.dao.findFirst("select * from user where userPhone=? ", phone);
        if (checkUser == null) {
            // 推广码验证和标准化（仅新用户注册时处理）
            String normalizedPromotionCode = null;
            if (StrKit.notBlank(promotionCode)) {
                if (!PromotionCodeKit.isValidFormat(promotionCode)) {
                    return RetKit.fail(PromotionCodeKit.getFormatErrorMessage());
                }
                normalizedPromotionCode = PromotionCodeKit.normalize(promotionCode);
            }

            User newUser = new User();
            String userImg = "/upload/avatar/img.jpg";
            String userNickName = phone;
            // web端用户分享码设置
            String identity = RandomKit.getRandomCharAndNum(6);
            newUser.setIdentity(identity);

            // 设置用户基本信息
            newUser.setUserNickName(userNickName).setUserPhone(phone).setUserImg(userImg)
                    .setRegisterType(User.REGISTER_TYPE_CAPTCHA)
                    .setStatus(User.STATUS_IS_REGISTER).setCreateTime(new Date());

            // 如果有推广码，设置推广码和绑定时间
            if (normalizedPromotionCode != null) {
                newUser.setPromotionCode(normalizedPromotionCode).setPromotionBindTime(new Date());
            }

            try {
                boolean succ = newUser.save();
                if (!succ) {
                    return RetKit.fail("登录失败！");
                }

                // 记录推广码绑定日志
                if (normalizedPromotionCode != null) {
                    LogKit.info("用户验证码登录时绑定推广码成功 - userId: " + newUser.getUserId() + ", promotionCode: " + normalizedPromotionCode);
                }
            } catch (Exception e) {
                LogKit.info("验证码登录失败，手机号：" + phone);
                LogKit.error("验证码登录失败:" + e.getMessage());
            }

            checkUser = newUser;
        }

        // 登录有效时间 7天(秒)
        long liveSeconds = 30 * 24 * 60 * 60;
//        long liveSeconds = 60;
        // 过期的时间戳
        long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
        // 生成accessToken
        String accessToken = HashKit.sha1(StrKit.getRandomUUID());
        // 创建session
        UserSession session = new UserSession();
        session.setUserId(checkUser.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp)
                .setCreateTime(new Date()).save();

        // 用户分享码设置
        String identity = checkUser.getIdentity();
        if (StrKit.isBlank(identity)) {
            identity = RandomKit.getRandomCharAndNum(6);
            checkUser.setIdentity(identity);
        }
        // 记录登录时间
        checkUser.setLastLoginTime(new Date()).update();
        // 清楚账号的盐和密码，不暴露出来
        checkUser.removeSensitiveInfo();
        CacheKit.put(CacheConstant.APP_USER, accessToken, checkUser);
        // 创建登录日志
        createLoginLog(checkUser.getUserId(), ipAddress, Integer.valueOf(platform));
        Kv kv = new Kv();
        kv = Kv.by("userId", checkUser.getUserId()).set("userNickName", checkUser.getUserNickName()).set("userPhone", phone)
                .set("shareCode", checkUser.getIdentity())
                .set("userImg", checkUser.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken)
                .set("vipInfo", checkUser.buildVipInfo());
        
        // 添加推广码信息
        kv = addPromotionCodeInfo(kv, checkUser);

        return RetKit.ok("data", kv).setMsg("登录成功");
    }

    /**
     * @param openId
     * @param loginType
     * @param platform
     * @param avatar
     * @param nickName
     * @return
     * @Title: fasterLogin
     * @Description:
     * @date 2019年3月15日 下午2:33:16
     * <AUTHOR>
     */
    public RetKit fasterLogin(String unionId, String openId, Integer loginType, String platform, String avatar, String nickName,
                              String promotionCode, String ip) {

        if (StrKit.isBlank(platform) || loginType == null) {
            return RetKit.fail("platform,loginType不能为空！");
        }
        if (StrKit.isBlank(unionId) && StrKit.isBlank(openId)) {
            return RetKit.fail("unionId,openId不能同时为空！");
        }
        if ((loginType != User.REGISTER_TYPE_QQ) && (loginType != User.REGISTER_TYPE_WX)) {
            return RetKit.fail("loginType只能传入0或者1！");
        }
        StringBuffer sql = new StringBuffer();
        if (loginType == User.REGISTER_TYPE_QQ) {
            // 已绑定手机号的qq用户也要纳入查询
          if (StrUtil.isNotEmpty(openId)) {
            sql.append("SELECT u.* FROM user u where qqId = '" + openId + "'" +
                    " union( " +
                    " select u.* from user_bind ub " +
                    "    inner join user u on u.userId = ub.userId " +
                    "    where ub.bindType = 0 and ub.bindId = '" + openId + "'" +
                    "    and not exists ( " +
                    "      SELECT * FROM user u where qqId = '" + openId + "'" +
                    "    )" +
                    " )");
              User checkUser = User.dao.findFirst(sql.toString());
              if (checkUser == null) {
                  sql=new StringBuffer();
                  sql.append("SELECT * FROM user" +
                          " where qqUnionId = '" + unionId + "'");
              }
          }else{
            sql.append("SELECT * FROM user" +
                        " where qqUnionId = '" + unionId + "'");
          }

        } else if (loginType == User.REGISTER_TYPE_WX) {
            // 微信优先通过openId 查询
            // 没有传openId，则传 unionId
            if (StrUtil.isNotEmpty(openId)) {
                sql.append("SELECT * FROM user" +
                        " where wxId = '" + openId + "'" +
                        " union ( " +
                        "   select * from  user" +
                        "   where wxUnionId = '" + unionId + "'" +
                        "    and not exists (" +
                        "       SELECT * FROM user where wxId = '" + openId + "'" +
                        "    ) " +
                        " ) ");
            } else {
                sql.append("SELECT * FROM user" +
                        " where wxUnionId = '" + unionId + "'");
            }
        } else {

        }
        sql.append(" limit 1");

        User checkUser = User.dao.findFirst(sql.toString());

        if (checkUser == null) {
            // 推广码验证和标准化（仅新用户注册时处理）
            String normalizedPromotionCode = null;
            if (StrKit.notBlank(promotionCode)) {
                if (!PromotionCodeKit.isValidFormat(promotionCode)) {
                    return RetKit.fail(PromotionCodeKit.getFormatErrorMessage());
                }
                normalizedPromotionCode = PromotionCodeKit.normalize(promotionCode);
            }

            String newNickName = nickName;

            if (containsEmoji(newNickName)) {
                LogKit.info("第三方登录用户昵称还有emoji：" + newNickName);
                if (loginType == User.REGISTER_TYPE_QQ) {
                    newNickName = "QQ用户_" + UUID.randomUUID().toString().replace("-", "").substring(0, 7);
                } else {
                    newNickName = "微信用户_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
                }
            }

            User user = new User();
            user.setUserNickName(newNickName);
            user.setUserImg(avatar);
            if (loginType == User.REGISTER_TYPE_QQ) {
                user.setQqId(openId);
                user.setQqUnionId(unionId);
            } else if (loginType == User.REGISTER_TYPE_WX) {
                user.setWxId(openId);
                user.setWxUnionId(unionId);
            } else {

            }
            user.setRegisterType(loginType);
            user.setStatus(User.STATUS_IS_REGISTER);
            user.setCreateTime(new Date());
            // web端用户分享码设置
            String identity = RandomKit.getRandomCharAndNum(6);
            user.setIdentity(identity);

            // 如果有推广码，设置推广码和绑定时间
            if (normalizedPromotionCode != null) {
                user.setPromotionCode(normalizedPromotionCode).setPromotionBindTime(new Date());
            }

            try {
                boolean succ = user.save();
                if (!succ) {
                    return RetKit.fail("登录失败！数据库操作异常！");
                }

                // 记录推广码绑定日志
                if (normalizedPromotionCode != null) {
                    LogKit.info("用户第三方登录时绑定推广码成功 - userId: " + user.getUserId() + ", promotionCode: " + normalizedPromotionCode);
                }
            } catch (Exception e) {
                LogKit.info("第三方登录失败，用户昵称" + newNickName);
                LogKit.error("第三方登录失败：" + e.getMessage());
            }

            checkUser = user;
        } else {
            if (loginType == User.REGISTER_TYPE_WX
                    && StrUtil.isEmpty(checkUser.getWxUnionId())
                    && StrUtil.isNotEmpty(unionId)) {
                checkUser.setWxUnionId(unionId);
                try {
                    boolean succ = checkUser.save();
                    if (!succ) {
                        LogKit.info("微信登录失败，用户openId" + checkUser.getWxId());
                    }
                } catch (Exception e) {
                    LogKit.info("微信登录失败，用户昵称" + checkUser.getUserNickName());
                    LogKit.error("微信登录失败：" + e.getMessage());
                }
            }else if (loginType == User.REGISTER_TYPE_QQ
                    && StrUtil.isEmpty(checkUser.getQqUnionId())
                    && StrUtil.isNotEmpty(unionId)) {
                checkUser.setQqUnionId(unionId);
                try {
                    boolean succ = checkUser.save();
                    if (!succ) {
                        LogKit.info("qq登录失败，用户openId" + checkUser.getQqId());
                    }
                } catch (Exception e) {
                    LogKit.info("qq登录失败，用户昵称" + checkUser.getUserNickName());
                    LogKit.error("qq登录失败：" + e.getMessage());
                }
            }
        }
        // 登录有效时间 7天(秒)
        long liveSeconds = 30 * 24 * 60 * 60;
        // 过期的时间戳
        long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
        // 生成accessToken
        String accessToken = HashKit.sha1(StrKit.getRandomUUID());
        // 创建session
        UserSession session = new UserSession();
        session.setUserId(checkUser.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp)
                .setCreateTime(new Date()).save();

        // 用户分享码设置
        String identity = checkUser.getIdentity();
        if (StrKit.isBlank(identity)) {
            identity = RandomKit.getRandomCharAndNum(6);
            checkUser.setIdentity(identity);
        }

        // 记录登录时间
        checkUser.setLastLoginTime(new Date()).update();
        // 清楚账号的盐和密码，不暴露出来
        checkUser.removeSensitiveInfo();
        // 放进缓存
        CacheKit.put(CacheConstant.APP_USER, accessToken, checkUser);
        // 创建登录日志
        createLoginLog(checkUser.getUserId(), ip, Integer.valueOf(platform));
        Kv kv = new Kv();
        kv = Kv.by("userId", checkUser.getUserId()).set("userNickName", checkUser.getUserNickName())
                .set("userPhone", checkUser.getUserPhone() == null ? "" : checkUser.getUserPhone())
                .set("shareCode", checkUser.getIdentity())
                .set("userImg", checkUser.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken)
                .set("vipInfo", checkUser.buildVipInfo());
        
        // 添加推广码信息
        kv = addPromotionCodeInfo(kv, checkUser);
        
        return RetKit.ok("data", kv).setMsg("登录成功");
    }

    public RetKit appleLogin(String appleUserId, String promotionCode, int loginType, String platform, String ip) {
        if (StrKit.isBlank(appleUserId)) {
            return RetKit.fail("苹果账号id不能为空！");
        }
        User exist = User.dao.findFirst("select * from user where appleLoginUserId=?", appleUserId.trim());
        if (exist == null) {
            // 推广码验证和标准化（仅新用户注册时处理）
            String normalizedPromotionCode = null;
            if (StrKit.notBlank(promotionCode)) {
                if (!PromotionCodeKit.isValidFormat(promotionCode)) {
                    return RetKit.fail(PromotionCodeKit.getFormatErrorMessage());
                }
                normalizedPromotionCode = PromotionCodeKit.normalize(promotionCode);
            }

            exist = new User();
            String nickName = "苹果用户_" + UUID.randomUUID().toString().replace("-", "").substring(0, 7);
            String userImg = "/upload/avatar/img.jpg";
            // web端用户分享码设置
            String identity = RandomKit.getRandomCharAndNum(6);
            exist.setIdentity(identity);

            exist.setUserNickName(nickName).setStatus(User.STATUS_IS_LOGINED).setUserImg(userImg)
                    .setRegisterType(loginType)
                    .setCreateTime(new Date()).setAppleLoginUserId(appleUserId);

            // 如果有推广码，设置推广码和绑定时间
            if (normalizedPromotionCode != null) {
                exist.setPromotionCode(normalizedPromotionCode).setPromotionBindTime(new Date());
            }

            boolean succ = exist.save();
            if (succ && normalizedPromotionCode != null) {
                // 记录推广码绑定日志
                LogKit.info("用户苹果登录时绑定推广码成功 - userId: " + exist.getUserId() + ", promotionCode: " + normalizedPromotionCode);
            }
        }
        // 登录有效时间 7天(秒)
        long liveSeconds = 30 * 24 * 60 * 60;
        // 过期的时间戳
        long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
        // 生成accessToken
        String accessToken = HashKit.sha1(StrKit.getRandomUUID());
        // 创建session
        UserSession session = new UserSession();
        session.setUserId(exist.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp).setCreateTime(new Date())
                .save();

        // 用户分享码设置
        String identity = exist.getIdentity();
        if (StrKit.isBlank(identity)) {
            identity = RandomKit.getRandomCharAndNum(6);
            exist.setIdentity(identity);
        }

        // 记录登录时间
        exist.setLastLoginTime(new Date()).update();
        // 清除账号的密码和盐
        exist.removeSensitiveInfo();
        // 放进缓存
        CacheKit.put(CacheConstant.APP_USER, accessToken, exist);
        // 创建登录日志
        createLoginLog(exist.getUserId(), ip, Integer.valueOf(platform));
        Kv kv = new Kv();
        kv = Kv.by("userId", exist.getUserId()).set("userNickName", exist.getUserNickName())
                .set("userPhone", exist.getUserPhone() == null ? "" : exist.getUserPhone())
                .set("shareCode", exist.getIdentity())
                .set("userImg", exist.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken)
                .set("vipInfo", exist.buildVipInfo());
        
        // 添加推广码信息
        kv = addPromotionCodeInfo(kv, exist);
        
        return RetKit.ok("data", kv).setMsg("登录成功");
    }

    /**
     * @param token
     * @return
     * @Title: onePassLogin  一键登录
     * @Description:
     * @date 2021年10月11日 下午2:33:16
     * <AUTHOR>
     */
    public RetKit onePassLogin(String token, String platform, String ip) {
        if (StrKit.isBlank(token)) {
            return RetKit.fail("token不能为空！");
        }
        String phone = new AliOpenapiKit().GetMobile(token);
        if (StrKit.isBlank(phone)) {
            return RetKit.fail("一键登录失败，手机号异常！");
        }

        User user = User.dao.findFirst("select * from user where userPhone=? ", phone);
        if (user == null) {
            user = new User();
            String userNickName = phone;
            String userImg = "/upload/avatar/img.jpg";
            // web端用户分享码设置
            String identity = RandomKit.getRandomCharAndNum(6);
            user.setIdentity(identity);

            user.setUserPhone(phone).setUserNickName(userNickName).setStatus(User.STATUS_IS_LOGINED)
                    .setUserImg(userImg).setRegisterType(User.REGISTER_TYPE_ALI_ONES).setCreateTime(new Date());
            user.save();
        }
        // 登录有效时间 7天(秒)
        long liveSeconds = 30 * 24 * 60 * 60;
        // 过期的时间戳
        long timeStamp = System.currentTimeMillis() + (liveSeconds * 1000);
        // 生成accessToken
        String accessToken = HashKit.sha1(StrKit.getRandomUUID());
        // 创建session
        UserSession session = new UserSession();
        session.setUserId(user.getUserId()).setSessionId(accessToken).setTimeStamp(timeStamp).setCreateTime(new Date())
                .save();

        // 用户分享码设置
        String identity = user.getIdentity();
        if (StrKit.isBlank(identity)) {
            identity = RandomKit.getRandomCharAndNum(6);
            user.setIdentity(identity);
        }

        // 记录登录时间
        user.setLastLoginTime(new Date()).update();
        // 清除账号的密码和盐
        user.removeSensitiveInfo();
        // 放进缓存
        CacheKit.put(CacheConstant.APP_USER, accessToken, user);
        // 创建登录日志
        createLoginLog(user.getUserId(), ip, Integer.valueOf(platform));
        Kv kv = new Kv();
        kv = Kv.by("userId", user.getUserId()).set("userNickName", user.getUserNickName())
                .set("userPhone", user.getUserPhone() == null ? "" : user.getUserPhone())
                .set("shareCode", user.getIdentity())
                .set("userImg", user.getUserImg()).set(Constant.APP_ACCESSTOKE, accessToken)
                .set("vipInfo", user.buildVipInfo());
        
        // 添加推广码信息
        kv = addPromotionCodeInfo(kv, user);
        
        return RetKit.ok("data", kv).setMsg("登录成功");
    }

    /**
     * @param accessToken
     * @return
     * @Title: logout
     * @Description: 退出登录
     * @date 2018年12月10日 下午6:36:51
     * <AUTHOR>
     */
    public boolean logout(String accessToken) {
        if (accessToken == null) {
            return false;
        }
        boolean succ = UserSession.dao.deleteById(accessToken);
        CacheKit.remove(CacheConstant.APP_USER, accessToken);
        return succ;
    }

    public User getUserCacheWithSessionId(String accessToken) {
        return CacheKit.get(CacheConstant.APP_USER, accessToken);
    }

    public RetKit retSetPwd(String phone, String password, String checkPwd) {
        if (StrKit.isBlank(phone)) {
            return RetKit.fail("请传入账号！");
        }
        User user = User.dao.findFirst("select * from user where userPhone=? limit 1", phone.trim());
        if (user == null) {
            return RetKit.fail("该手机号码未注册！");
        }
        if (!checkPwd.equals(password)) {
            return RetKit.fail("两次密码不一致！");
        }
        String salt = HashKit.generateSaltForSha256();
        String hashPwd = HashKit.sha256(salt + password);
        boolean succ = user.setSalt(salt).setUserPass(hashPwd).update();
        if (succ) {
            removeCacheAndSession(user.getUserId());
        }

        return succ ? RetKit.ok() : RetKit.fail();
    }

    private void createLoginLog(Integer userId, String ipAddress, Integer platform) {
        Record record = new Record();
        record.set("userId", userId).set("ip", ipAddress).set("platform", platform).set("loginTime", new Date());
        Db.save("user_login_log", record);
    }

    private void removeCacheAndSession(Integer userId) {
        List<UserSession> list = UserSession.dao.find("select * from user_session where userId=?", userId);
        for (UserSession cus : list) {
            CacheKit.remove(CacheConstant.APP_USER, cus.getSessionId());
        }
        Db.update("delete from user_session where userId=?", userId);
    }

    public User loginWithSessionId(String sessionId, String ipAddress, Integer platform) {
        UserSession session = UserSession.dao.findById(sessionId);
        if (session == null) {
            return null;
        }
        // 判断sessionId是否已过期，如果过期则删除
        if (session.isExpired()) {
            session.delete();
            return null;
        }

        User user = User.dao.findById(session.getUserId());
        if (user != null) {
            user.setLastLoginTime(new Date()).update();
            user.removeSensitiveInfo();
            user.put("sessionId", sessionId);
            CacheKit.put(CacheConstant.APP_USER, sessionId, user);
            createLoginLog(user.getUserId(), ipAddress, platform);
            return user;
        }

        return null;
    }

    /**
     * 为登录返回数据添加推广码信息
     * 
     * @param kv 登录返回的数据对象
     * @param user 用户对象
     * @return 包含推广码信息的数据对象
     */
    private Kv addPromotionCodeInfo(Kv kv, User user) {
        String promotionCode = user.getPromotionCode();
        boolean hasPromotionCode = StrKit.notBlank(promotionCode);
        
        kv.set("promotionCode", hasPromotionCode ? promotionCode : null)
          .set("promotionBindTime", hasPromotionCode ? user.getPromotionBindTime() : null)
          .set("hasPromotionCode", hasPromotionCode);
        
        return kv;
    }

}
