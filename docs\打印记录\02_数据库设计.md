# 打印记录数据库设计

## 1. 设计原则

- 采用统一的打印记录表，通过类型字段区分不同的打印类型
- 使用软删除机制，保留历史数据
- 支持高效的分页查询和搜索
- 预留扩展字段，便于后续功能扩展

## 2. 数据库表设计

### 2.1 打印记录主表 (print_record)

```sql
CREATE TABLE `print_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `print_type` tinyint(4) NOT NULL COMMENT '打印类型：1-模板打印，2-文档打印，3-图片打印',
  `source_id` int(11) DEFAULT NULL COMMENT '源数据ID（模板ID、文档ID、图片ID等）',
  `source_name` varchar(255) NOT NULL COMMENT '源数据名称（模板名称、文档名称、图片名称）',
  `source_cover` varchar(500) DEFAULT NULL COMMENT '预览图URL',
  `print_width` int(11) DEFAULT NULL COMMENT '打印宽度（mm）',
  `print_height` int(11) DEFAULT NULL COMMENT '打印高度（mm）',
  `print_copies` int(11) NOT NULL DEFAULT '1' COMMENT '打印份数',
  `print_platform` tinyint(4) NOT NULL COMMENT '打印端：1-手机iOS，2-手机安卓，3-电脑Windows，4-电脑Mac',
  `print_time` datetime NOT NULL COMMENT '打印时间',
  `source_data` text COMMENT '源数据内容（JSON格式，用于重新打印）',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型（PDF、JPG、PNG等）',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `print_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '打印状态：1-成功，2-失败',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_print_type` (`print_type`),
  KEY `idx_print_time` (`print_time`),
  KEY `idx_delete_time` (`delete_time`),
  KEY `idx_user_type_time` (`user_id`, `print_type`, `print_time`),
  KEY `idx_source_name` (`source_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打印记录表';
```

### 2.2 打印端类型枚举

```java
// 打印类型
public static final int PRINT_TYPE_TEMPLATE = 1;  // 模板打印
public static final int PRINT_TYPE_DOCUMENT = 2;  // 文档打印  
public static final int PRINT_TYPE_IMAGE = 3;     // 图片打印

// 打印端类型
public static final int PLATFORM_IOS = 1;         // 手机iOS
public static final int PLATFORM_ANDROID = 2;     // 手机安卓
public static final int PLATFORM_WINDOWS = 3;     // 电脑Windows
public static final int PLATFORM_MAC = 4;         // 电脑Mac

// 打印状态
public static final int PRINT_STATUS_SUCCESS = 1; // 打印成功
public static final int PRINT_STATUS_FAILED = 2;  // 打印失败
```

## 3. 索引设计说明

- **主键索引**：`id` 自增主键
- **用户索引**：`idx_user_id` 用于按用户查询
- **类型索引**：`idx_print_type` 用于按打印类型查询
- **时间索引**：`idx_print_time` 用于时间排序
- **软删除索引**：`idx_delete_time` 用于软删除查询
- **复合索引**：`idx_user_type_time` 用于用户+类型+时间的复合查询
- **搜索索引**：`idx_source_name` 用于名称搜索

## 4. 数据存储说明

### 4.1 模板打印记录
- `source_id`：存储模板ID（templet表的id）
- `source_name`：存储模板名称
- `source_cover`：存储模板预览图URL
- `source_data`：存储模板的完整数据（JSON格式），用于重新打印
- `print_width/print_height`：存储打印尺寸

### 4.2 文档打印记录
- `source_id`：存储文档ID（如果有文档表）
- `source_name`：存储文档名称
- `file_type`：存储文件类型（PDF等）
- `file_size`：存储文件大小
- `source_data`：存储文档相关信息

### 4.3 图片打印记录
- `source_id`：存储图片ID（如果有图片表）
- `source_name`：存储图片名称
- `source_cover`：存储图片预览URL
- `file_type`：存储图片格式（JPG、PNG等）
- `file_size`：存储图片大小
- `source_data`：存储图片处理参数

## 5. 查询优化

### 5.1 分页查询
```sql
-- 模板打印记录查询（默认100条）
SELECT * FROM print_record 
WHERE user_id = ? AND print_type = 1 AND delete_time IS NULL
ORDER BY print_time DESC 
LIMIT 0, 100;

-- 文档打印记录查询（默认20条）
SELECT * FROM print_record 
WHERE user_id = ? AND print_type = 2 AND delete_time IS NULL
ORDER BY print_time DESC 
LIMIT 0, 20;
```

### 5.2 搜索查询
```sql
-- 按名称搜索
SELECT * FROM print_record 
WHERE user_id = ? AND print_type = ? AND delete_time IS NULL
AND source_name LIKE CONCAT('%', ?, '%')
ORDER BY print_time DESC;

-- 按尺寸搜索（仅模板支持）
SELECT * FROM print_record 
WHERE user_id = ? AND print_type = 1 AND delete_time IS NULL
AND print_width = ? AND print_height = ?
ORDER BY print_time DESC;
```

## 6. 数据迁移考虑

由于是新增功能，不需要迁移历史数据。但需要在现有的打印功能中集成记录保存逻辑。
