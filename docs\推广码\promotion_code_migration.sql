-- 推广码功能数据库迁移脚本
-- 执行时间：2024-07-30
-- 目的：为用户表添加推广码支持，实现用户推广码绑定功能
-- 版本：v1.0

-- ============================================================================
-- 1. 数据库备份提醒
-- ============================================================================
-- 执行此脚本前，请确保已备份相关数据库表
-- 备份命令示例：
-- mysqldump -u username -p database_name user > user_backup_20240730.sql

-- ============================================================================
-- 2. 添加推广码相关字段
-- ============================================================================

-- 2.1 添加推广码字段
ALTER TABLE `user` ADD COLUMN `promotion_code` VARCHAR(50) DEFAULT NULL COMMENT '绑定的推广码';

-- 2.2 添加推广码绑定时间字段
ALTER TABLE `user` ADD COLUMN `promotion_bind_time` DATETIME DEFAULT NULL COMMENT '推广码绑定时间';

-- ============================================================================
-- 3. 创建索引以优化查询性能
-- ============================================================================

-- 3.1 推广码查询索引（支持按推广码查找用户）
CREATE INDEX `idx_promotion_code` ON `user` (`promotion_code`);

-- 3.2 绑定时间查询索引（支持按时间范围统计）
CREATE INDEX `idx_promotion_bind_time` ON `user` (`promotion_bind_time`);

-- 3.3 复合索引（支持推广码+时间的复合查询，用于统计分析）
CREATE INDEX `idx_promotion_code_time` ON `user` (`promotion_code`, `promotion_bind_time`);

-- ============================================================================
-- 4. 验证字段添加成功
-- ============================================================================

-- 4.1 检查字段是否添加成功
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user' 
AND TABLE_SCHEMA = DATABASE()
AND COLUMN_NAME IN ('promotion_code', 'promotion_bind_time');

-- 4.2 检查索引是否创建成功
SHOW INDEX FROM `user` WHERE Key_name LIKE 'idx_promotion%';

-- 4.3 查看表结构（可选）
-- DESCRIBE `user`;

-- ============================================================================
-- 5. 数据完整性检查
-- ============================================================================

-- 5.1 检查现有用户数据是否受影响
SELECT COUNT(*) as total_users FROM `user`;

-- 5.2 检查新字段的默认值
SELECT 
    COUNT(*) as users_with_null_promotion_code
FROM `user` 
WHERE promotion_code IS NULL;

-- 5.3 验证字段约束
SELECT 
    COUNT(*) as users_with_promotion_code
FROM `user` 
WHERE promotion_code IS NOT NULL;

-- ============================================================================
-- 6. 性能测试查询（可选执行）
-- ============================================================================

-- 6.1 测试推广码查询性能
-- EXPLAIN SELECT * FROM `user` WHERE promotion_code = 'TEST123';

-- 6.2 测试时间范围查询性能
-- EXPLAIN SELECT * FROM `user` WHERE promotion_bind_time BETWEEN '2024-07-01' AND '2024-07-31';

-- 6.3 测试复合查询性能
-- EXPLAIN SELECT * FROM `user` WHERE promotion_code = 'TEST123' AND promotion_bind_time > '2024-07-01';

-- ============================================================================
-- 7. 常用统计查询示例
-- ============================================================================

-- 7.1 查询绑定推广码的用户总数
-- SELECT COUNT(*) as bound_users FROM user WHERE promotion_code IS NOT NULL;

-- 7.2 查询各推广码的用户数量分布
-- SELECT 
--     promotion_code,
--     COUNT(*) as user_count,
--     MIN(promotion_bind_time) as first_bind_time,
--     MAX(promotion_bind_time) as last_bind_time
-- FROM user 
-- WHERE promotion_code IS NOT NULL 
-- GROUP BY promotion_code 
-- ORDER BY user_count DESC;

-- 7.3 查询指定时间范围内的推广码绑定情况
-- SELECT 
--     DATE(promotion_bind_time) as bind_date,
--     COUNT(*) as daily_binds
-- FROM user 
-- WHERE promotion_bind_time BETWEEN '2024-07-01' AND '2024-07-31'
-- GROUP BY DATE(promotion_bind_time)
-- ORDER BY bind_date;

-- ============================================================================
-- 8. 回滚脚本（紧急情况使用）
-- ============================================================================

-- 注意：以下回滚脚本仅在紧急情况下使用
-- 执行前请确保数据已备份，并评估回滚的影响

-- 8.1 删除索引
-- DROP INDEX `idx_promotion_code` ON `user`;
-- DROP INDEX `idx_promotion_bind_time` ON `user`;
-- DROP INDEX `idx_promotion_code_time` ON `user`;

-- 8.2 删除字段
-- ALTER TABLE `user` DROP COLUMN `promotion_code`;
-- ALTER TABLE `user` DROP COLUMN `promotion_bind_time`;

-- ============================================================================
-- 9. 执行完成确认
-- ============================================================================

-- 执行完成后，请确认以下内容：
-- ✓ 字段添加成功：promotion_code, promotion_bind_time
-- ✓ 索引创建成功：idx_promotion_code, idx_promotion_bind_time, idx_promotion_code_time
-- ✓ 现有用户数据完整性未受影响
-- ✓ 新字段默认值为NULL，符合预期

SELECT 'Migration completed successfully!' as status;

-- ============================================================================
-- 10. 后续开发注意事项
-- ============================================================================

-- 10.1 BaseUser模型类需要重新生成，以包含新字段的getter/setter方法
-- 10.2 推广码字段允许为NULL，业务逻辑中需要进行空值检查
-- 10.3 推广码绑定时，promotion_code和promotion_bind_time应该同时更新
-- 10.4 建议在应用层实现推广码格式验证：6-8位字母数字组合
-- 10.5 推广码建议转换为大写存储，保持一致性

-- ============================================================================
-- 脚本执行完毕
-- ============================================================================
