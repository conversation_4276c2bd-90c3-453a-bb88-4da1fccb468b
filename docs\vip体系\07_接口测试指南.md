# VIP体系接口测试指南

## 测试前准备

1. 确保数据库表已创建完成
2. 在 vip_feature_rule 表中插入OCR规则：
```sql
INSERT INTO `vip_feature_rule` (`method`, `pattern`, `permission`, `enabled`, `create_time`, `update_time`) 
VALUES ('POST', '/api/v2/ocr/recognize', 'ocr_create', 1, NOW(), NOW());
```

## 接口测试流程

### 1. 获取VIP套餐信息
```
GET /api/v2/vip/plans
```
预期返回：月度和年度套餐信息

### 2. 获取用户VIP状态
```
GET /api/v2/vip/status
Headers: Authorization: Bearer {token}
```
预期返回：用户当前VIP状态、到期时间等

### 3. 测试OCR权限拦截（非VIP用户）
```
POST /api/v2/ocr/recognize
Headers: Authorization: Bearer {token}
Body: {图片数据}
```
预期返回：VIP权限不足的错误提示

### 4. 创建VIP订单
```
POST /api/v2/vip/order/create
Headers: Authorization: Bearer {token}
Body: {
  "plan": "monthly",
  "channel": "wechat"
}
```
预期返回：订单号和支付参数

### 5. 查询订单详情
```
GET /api/v2/vip/order/get?orderNo={orderNo}
Headers: Authorization: Bearer {token}
```
预期返回：订单详细信息

### 6. 模拟支付回调（测试环境）
```
POST /api/v2/vip/pay/wechat/notify
Headers: 
  Wechatpay-Timestamp: {timestamp}
  Wechatpay-Nonce: {nonce}
  Wechatpay-Signature: {signature}
  Wechatpay-Serial: {serial}
Body: {微信回调数据}
```
注意：生产环境此接口由微信服务器调用

### 7. 验证VIP开通后的权限
重复步骤3，预期OCR接口可以正常访问

## 错误码说明

- `needVip: true` - 需要VIP权限
- `needLogin: true` - 需要登录
- `upgradeUrl: "/vip/purchase"` - 升级引导地址

## 测试数据

### 测试用户
- 免费用户：userTier = "FREE"
- VIP用户：userTier = "VIP_MONTHLY" 或 "VIP_YEARLY"

### 测试订单状态
- created: 待支付
- paid: 已支付
- closed: 已关闭
- failed: 支付失败

## 注意事项

1. 微信支付配置未完成时，会返回模拟支付参数
2. VIP权限拦截器基于数据库规则，可动态配置
3. 订单创建前会检查是否有未支付订单
4. 支付回调需要验签，测试时可临时跳过验签逻辑
