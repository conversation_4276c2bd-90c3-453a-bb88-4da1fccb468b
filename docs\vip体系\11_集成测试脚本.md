# VIP体系集成测试脚本

## 测试环境准备

### 1. 数据库准备
```sql
-- 创建完整的测试用户（包含必填字段）
INSERT INTO user (
    userId, userNickName, userPhone, userImg, userPass,
    registerType, salt, status, userTier, vipExpireTime,
    identity, createTime
) VALUES (
    9999,
    'VIP测试用户',
    '13800139999',
    '/upload/avatar/img.jpg',
    'test_password_hash',
    2, -- REGISTER_TYPE_PHONE
    'test_salt_123456',
    1, -- STATUS_IS_REGISTER
    'FREE',
    NULL,
    'TEST99',
    NOW()
);

-- 清理测试数据
DELETE FROM vip_order WHERE user_id = 9999;
DELETE FROM vip_feature_rule WHERE pattern LIKE '%test%';
DELETE FROM user WHERE userId = 9999; -- 清理可能存在的测试用户
```

### 2. 测试配置
```bash
# 设置测试环境变量
export TEST_USER_ID=9999
export TEST_BASE_URL=http://localhost:8080
export TEST_TOKEN=your_test_token
```

## 自动化测试脚本

### 1. 基础功能测试
```bash
#!/bin/bash
# test_vip_basic.sh

BASE_URL=${TEST_BASE_URL:-http://localhost:8080}
USER_ID=${TEST_USER_ID:-9999}
TOKEN=${TEST_TOKEN:-test_token}

echo "开始VIP体系基础功能测试..."

# 测试1: 获取VIP套餐信息
echo "测试1: 获取VIP套餐信息"
response=$(curl -s "$BASE_URL/api/v2/vip/plans")
if echo "$response" | grep -q "monthly"; then
    echo "✓ 套餐信息接口正常"
else
    echo "✗ 套餐信息接口异常: $response"
    exit 1
fi

# 测试2: 获取用户VIP状态
echo "测试2: 获取用户VIP状态"
response=$(curl -s -H "Authorization: Bearer $TOKEN" "$BASE_URL/api/v2/vip/status")
if echo "$response" | grep -q "isVip"; then
    echo "✓ VIP状态接口正常"
else
    echo "✗ VIP状态接口异常: $response"
    exit 1
fi

# 测试3: 创建VIP订单
echo "测试3: 创建VIP订单"
response=$(curl -s -X POST \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"plan":"monthly","channel":"wechat"}' \
    "$BASE_URL/api/v2/vip/order/create")

if echo "$response" | grep -q "orderNo"; then
    ORDER_NO=$(echo "$response" | grep -o '"orderNo":"[^"]*"' | cut -d'"' -f4)
    echo "✓ 订单创建成功: $ORDER_NO"
else
    echo "✗ 订单创建失败: $response"
    exit 1
fi

# 测试4: 查询订单详情
echo "测试4: 查询订单详情"
response=$(curl -s -H "Authorization: Bearer $TOKEN" \
    "$BASE_URL/api/v2/vip/order/get?orderNo=$ORDER_NO")
if echo "$response" | grep -q "$ORDER_NO"; then
    echo "✓ 订单查询正常"
else
    echo "✗ 订单查询异常: $response"
    exit 1
fi

echo "基础功能测试完成"
```

### 2. 权限拦截测试
```bash
#!/bin/bash
# test_vip_permission.sh

BASE_URL=${TEST_BASE_URL:-http://localhost:8080}
TOKEN=${TEST_TOKEN:-test_token}

echo "开始VIP权限拦截测试..."

# 确保测试用户是免费用户
mysql -u$DB_USER -p$DB_PASS -e "UPDATE user SET userTier='FREE', vipExpireTime=NULL WHERE userId=$USER_ID;" $DB_NAME

# 测试OCR权限拦截
echo "测试OCR权限拦截（免费用户）"
response=$(curl -s -X POST \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"image":"test_image_data"}' \
    "$BASE_URL/api/v2/ocr/recognize")

if echo "$response" | grep -q "needVip"; then
    echo "✓ VIP权限拦截正常"
else
    echo "✗ VIP权限拦截失败: $response"
    exit 1
fi

# 模拟开通VIP
echo "模拟开通VIP"
mysql -u$DB_USER -p$DB_PASS -e "UPDATE user SET userTier='VIP_MONTHLY', vipExpireTime=DATE_ADD(NOW(), INTERVAL 1 MONTH) WHERE userId=$USER_ID;" $DB_NAME

# 再次测试OCR接口
echo "测试OCR接口（VIP用户）"
response=$(curl -s -X POST \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"image":"test_image_data"}' \
    "$BASE_URL/api/v2/ocr/recognize")

if echo "$response" | grep -q "needVip"; then
    echo "✗ VIP用户仍被拦截: $response"
    exit 1
else
    echo "✓ VIP用户可正常访问"
fi

echo "权限拦截测试完成"
```

### 3. 支付回调测试
```bash
#!/bin/bash
# test_vip_callback.sh

BASE_URL=${TEST_BASE_URL:-http://localhost:8080}

echo "开始支付回调测试..."

# 创建测试订单
ORDER_NO="VIP$(date +%s)9999"
mysql -u$DB_USER -p$DB_PASS -e "INSERT INTO vip_order (order_no, user_id, plan, tier, amount, channel, status, create_time, update_time) VALUES ('$ORDER_NO', $USER_ID, 'monthly', 'VIP_MONTHLY', 990, 'wechat', 'created', NOW(), NOW());" $DB_NAME

# 模拟微信支付回调
echo "模拟微信支付回调"
callback_data='{
    "id": "test_callback_id",
    "create_time": "2024-01-01T10:00:00+08:00",
    "resource_type": "encrypt-resource",
    "event_type": "TRANSACTION.SUCCESS",
    "summary": "支付成功",
    "resource": {
        "original_type": "transaction",
        "algorithm": "AEAD_AES_256_GCM",
        "ciphertext": "test_encrypted_data",
        "associated_data": "transaction",
        "nonce": "test_nonce"
    }
}'

response=$(curl -s -X POST \
    -H "Wechatpay-Timestamp: $(date +%s)" \
    -H "Wechatpay-Nonce: test_nonce_$(date +%s)" \
    -H "Wechatpay-Signature: test_signature" \
    -H "Wechatpay-Serial: test_serial" \
    -H "Content-Type: application/json" \
    -d "$callback_data" \
    "$BASE_URL/api/v2/vip/pay/wechat/notify")

if echo "$response" | grep -q "SUCCESS\|FAIL"; then
    echo "✓ 回调接口响应正常: $response"
else
    echo "✗ 回调接口异常: $response"
    exit 1
fi

echo "支付回调测试完成"
```

### 4. 性能测试
```bash
#!/bin/bash
# test_vip_performance.sh

BASE_URL=${TEST_BASE_URL:-http://localhost:8080}
TOKEN=${TEST_TOKEN:-test_token}

echo "开始VIP体系性能测试..."

# 并发创建订单测试
echo "并发创建订单测试（10个并发）"
for i in {1..10}; do
    (
        response=$(curl -s -X POST \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d '{"plan":"monthly","channel":"wechat"}' \
            "$BASE_URL/api/v2/vip/order/create")
        echo "请求$i: $(echo "$response" | grep -o '"success":[^,]*')"
    ) &
done
wait

# VIP状态查询压力测试
echo "VIP状态查询压力测试（50次请求）"
start_time=$(date +%s)
for i in {1..50}; do
    curl -s -H "Authorization: Bearer $TOKEN" "$BASE_URL/api/v2/vip/status" > /dev/null
done
end_time=$(date +%s)
duration=$((end_time - start_time))
echo "50次请求耗时: ${duration}秒，平均响应时间: $((duration * 1000 / 50))ms"

echo "性能测试完成"
```

## 数据验证脚本

### 1. 数据一致性检查
```sql
-- check_vip_data_consistency.sql

-- 检查订单状态一致性
SELECT '订单状态一致性检查' as check_type;
SELECT 
    COUNT(*) as inconsistent_orders,
    'paid订单但用户仍为FREE' as issue
FROM vip_order o 
JOIN user u ON o.user_id = u.userId 
WHERE o.status = 'paid' AND u.userTier = 'FREE';

-- 检查VIP到期时间
SELECT 'VIP到期时间检查' as check_type;
SELECT 
    COUNT(*) as expired_vip_users,
    'VIP用户但已过期' as issue
FROM user 
WHERE userTier != 'FREE' AND vipExpireTime < NOW();

-- 检查重复未支付订单
SELECT '重复订单检查' as check_type;
SELECT 
    user_id,
    COUNT(*) as unpaid_orders,
    '同用户多个未支付订单' as issue
FROM vip_order 
WHERE status = 'created' 
GROUP BY user_id 
HAVING COUNT(*) > 1;
```

### 2. 统计数据验证
```sql
-- vip_stats_validation.sql

-- VIP转化率统计
SELECT 
    '用户统计' as stat_type,
    COUNT(*) as total_users,
    SUM(CASE WHEN userTier != 'FREE' THEN 1 ELSE 0 END) as vip_users,
    ROUND(SUM(CASE WHEN userTier != 'FREE' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as conversion_rate
FROM user;

-- 订单统计
SELECT 
    '订单统计' as stat_type,
    COUNT(*) as total_orders,
    SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_orders,
    ROUND(SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate,
    SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) / 100.0 as total_revenue_yuan
FROM vip_order;

-- 功能使用统计
SELECT 
    '功能统计' as stat_type,
    COUNT(*) as enabled_rules
FROM vip_feature_rule 
WHERE enabled = 1;
```

## 测试报告生成

### 测试结果汇总脚本
```bash
#!/bin/bash
# generate_test_report.sh

REPORT_FILE="vip_test_report_$(date +%Y%m%d_%H%M%S).txt"

echo "VIP体系测试报告" > $REPORT_FILE
echo "生成时间: $(date)" >> $REPORT_FILE
echo "===========================================" >> $REPORT_FILE

# 执行各项测试并记录结果
echo "1. 基础功能测试" >> $REPORT_FILE
./test_vip_basic.sh >> $REPORT_FILE 2>&1

echo -e "\n2. 权限拦截测试" >> $REPORT_FILE
./test_vip_permission.sh >> $REPORT_FILE 2>&1

echo -e "\n3. 支付回调测试" >> $REPORT_FILE
./test_vip_callback.sh >> $REPORT_FILE 2>&1

echo -e "\n4. 性能测试" >> $REPORT_FILE
./test_vip_performance.sh >> $REPORT_FILE 2>&1

echo -e "\n5. 数据一致性检查" >> $REPORT_FILE
mysql -u$DB_USER -p$DB_PASS $DB_NAME < check_vip_data_consistency.sql >> $REPORT_FILE 2>&1

echo "测试报告已生成: $REPORT_FILE"
```

## 测试数据清理脚本

### 测试完成后清理
```sql
-- 清理测试订单
DELETE FROM vip_order WHERE user_id = 9999;
DELETE FROM vip_delivery_log WHERE user_id = 9999;
DELETE FROM vip_pay_notify_log WHERE order_no LIKE 'VIP%9999';

-- 清理测试用户
DELETE FROM user WHERE userId = 9999;

-- 清理测试规则（如果有）
DELETE FROM vip_feature_rule WHERE pattern LIKE '%test%';
```

### 完整的测试用户创建脚本
```sql
-- 如果需要创建更真实的测试用户
INSERT INTO user (
    userId, userNickName, userPhone, userImg, userPass,
    registerType, salt, status, userTier, vipExpireTime,
    identity, createTime, lastLoginTime
) VALUES (
    9999,
    'VIP测试用户_' || DATE_FORMAT(NOW(), '%m%d%H%i'),
    '13800139999',
    '/upload/avatar/img.jpg',
    SHA2(CONCAT('test_salt_123456', 'test123456'), 256), -- 模拟真实密码哈希
    2, -- REGISTER_TYPE_PHONE
    'test_salt_123456',
    2, -- STATUS_IS_LOGINED（已登录状态，便于测试）
    'FREE',
    NULL,
    CONCAT('TEST', LPAD(FLOOR(RAND() * 10000), 4, '0')), -- 随机identity
    NOW(),
    NOW()
);
```

## 注意事项

1. **测试环境隔离**：确保测试不影响生产数据
2. **清理测试数据**：测试完成后执行清理脚本
3. **权限配置**：确保测试用户有足够权限
4. **网络环境**：确保测试环境网络正常
5. **依赖服务**：确保数据库、缓存等依赖服务正常
6. **用户ID冲突**：确保测试用户ID（9999）不与真实用户冲突
