# 数据库设计（v1）

## 1. user 表扩展
- 新增字段：vipExpireTime DATETIME NULL COMMENT 'VIP到期时间'
- 已有字段：userTier VARCHAR(20) - FREE/VIP_MONTHLY/VIP_YEARLY

变更 SQL（MySQL）：
```sql
ALTER TABLE `user`
  ADD COLUMN `vipExpireTime` DATETIME NULL COMMENT 'VIP到期时间' AFTER `userTier`,
  ADD INDEX `idx_user_vip_expire`(`vipExpireTime`);
```

## 2. vip_order 表
```sql
CREATE TABLE `vip_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号',
  `user_id` INT NOT NULL,
  `plan` VARCHAR(20) NOT NULL COMMENT '套餐: monthly/yearly',
  `tier` VARCHAR(20) NOT NULL COMMENT '用户等级: VIP_MONTHLY/VIP_YEARLY',
  `amount` INT NOT NULL COMMENT '金额(分)',
  `channel` VARCHAR(20) NOT NULL COMMENT '支付渠道: wechat',
  `status` VARCHAR(20) NOT NULL COMMENT '状态: created/paid/closed/failed',
  `extra` JSON NULL COMMENT '渠道返回的扩展字段',
  `create_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP订单';

-- 审计增强字段（建议）
ALTER TABLE `vip_order`
  ADD COLUMN `channel_trade_no` VARCHAR(64) NULL COMMENT '微信transaction_id' AFTER `status`,
  ADD COLUMN `paid_time` DATETIME NULL COMMENT '支付成功时间' AFTER `channel_trade_no`,
  ADD COLUMN `notify_time` DATETIME NULL COMMENT '回调到达时间' AFTER `paid_time`;
CREATE INDEX `idx_vip_order_paid_time` ON `vip_order` (`paid_time`);
CREATE INDEX `idx_vip_order_channel_trade_no` ON `vip_order` (`channel_trade_no`);

```

## 3. vip_delivery_log 表（可选）
```sql
CREATE TABLE `vip_delivery_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `order_no` VARCHAR(64) NOT NULL,
  `user_id` INT NOT NULL,
  `action` VARCHAR(32) NOT NULL COMMENT 'open/extend',
  `from_tier` VARCHAR(20) NOT NULL,
  `to_tier` VARCHAR(20) NOT NULL,
  `from_expire` DATETIME NULL,
  `to_expire` DATETIME NULL,
  `create_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP开通/续期日志';
```

## 4. vip_feature_rule（接口-权限映射，可选但推荐）
```sql
CREATE TABLE `vip_feature_rule` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `method` VARCHAR(10) NOT NULL COMMENT 'HTTP 方法，如 GET/POST',
  `pattern` VARCHAR(255) NOT NULL COMMENT '路径模式，支持前缀匹配或通配符，如 /api/v2/ocr/recognize',
  `permission` VARCHAR(64) NOT NULL COMMENT 'PermissionType 代码，如 ocr_create',
  `enabled` TINYINT(1) NOT NULL DEFAULT 1,
  `create_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_method_pattern` (`method`, `pattern`(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP 功能拦截规则';
```


