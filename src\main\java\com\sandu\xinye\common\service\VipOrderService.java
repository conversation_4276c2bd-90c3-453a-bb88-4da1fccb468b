package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.enums.UserTier;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.User;
import com.sandu.xinye.common.model.VipOrder;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * VIP订单服务
 * 处理VIP订单的创建、查询、状态管理
 */
public class VipOrderService {
    
    public static final VipOrderService me = new VipOrderService();
    
    // 套餐价格配置（分）
    private static final int MONTHLY_PRICE = 990;  // 9.9元
    private static final int YEARLY_PRICE = 9900;  // 99元
    
    /**
     * 创建VIP订单
     */
    public RetKit createOrder(Integer userId, String plan) {
        try {
            // 参数验证
            if (userId == null) {
                return RetKit.fail("用户ID不能为空");
            }
            
            if (!isValidPlan(plan)) {
                return RetKit.fail("无效的套餐类型");
            }
            
            // 检查用户是否存在
            User user = User.dao.findById(userId);
            if (user == null) {
                return RetKit.fail("用户不存在");
            }
            
            // 检查是否有未支付的订单
            VipOrder existingOrder = findUnpaidOrder(userId);
            if (existingOrder != null) {
                return RetKit.fail("您有未支付的订单，请先完成支付或取消订单")
                    .set("existingOrderNo", existingOrder.getOrderNo());
            }
            
            // 创建订单
            VipOrder order = buildOrder(userId, plan);
            boolean saveSuccess = order.save();
            
            if (!saveSuccess) {
                return RetKit.fail("创建订单失败");
            }
            
            LogKit.info("VIP订单创建成功: " + order.getOrderNo() + ", 用户: " + userId + ", 套餐: " + plan);
            
            // 生成支付参数
            Map<String, Object> payParams = generatePayParams(order);
            
            return RetKit.ok("订单创建成功")
                .set("orderNo", order.getOrderNo())
                .set("amount", order.getAmountYuan())
                .set("plan", plan)
                .set("payParams", payParams);
            
        } catch (Exception e) {
            LogKit.error("创建VIP订单失败", e);
            return RetKit.fail("创建订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询订单详情
     */
    public RetKit getOrder(Integer userId, String orderNo) {
        try {
            if (userId == null || StrKit.isBlank(orderNo)) {
                return RetKit.fail("参数不能为空");
            }
            
            VipOrder order = VipOrder.dao.findFirst(
                "SELECT * FROM vip_order WHERE order_no = ? AND user_id = ?", 
                orderNo, userId
            );
            
            if (order == null) {
                return RetKit.fail("订单不存在");
            }
            
            Map<String, Object> orderInfo = buildOrderInfo(order);
            
            return RetKit.ok("查询成功").set("order", orderInfo);
            
        } catch (Exception e) {
            LogKit.error("查询VIP订单失败", e);
            return RetKit.fail("查询订单失败");
        }
    }
    
    /**
     * 查询用户的订单列表
     */
    public RetKit getUserOrders(Integer userId, int page, int pageSize) {
        try {
            if (userId == null) {
                return RetKit.fail("用户ID不能为空");
            }
            
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 10;
            
            String sql = "SELECT * FROM vip_order WHERE user_id = ? ORDER BY create_time DESC LIMIT ?, ?";
            int offset = (page - 1) * pageSize;
            
            java.util.List<VipOrder> orders = VipOrder.dao.find(sql, userId, offset, pageSize);
            
            java.util.List<Map<String, Object>> orderList = new java.util.ArrayList<>();
            for (VipOrder order : orders) {
                orderList.add(buildOrderInfo(order));
            }
            
            return RetKit.ok("查询成功")
                .set("orders", orderList)
                .set("page", page)
                .set("pageSize", pageSize);
            
        } catch (Exception e) {
            LogKit.error("查询用户订单列表失败", e);
            return RetKit.fail("查询失败");
        }
    }
    
    /**
     * 构建订单对象
     */
    private VipOrder buildOrder(Integer userId, String plan) {
        VipOrder order = new VipOrder();
        Date now = new Date();
        
        order.setOrderNo(generateOrderNo())
             .setUserId(userId)
             .setPlan(plan)
             .setTier(planToTier(plan).name())
             .setAmount(getPlanPrice(plan))
             .setChannel(VipOrder.CHANNEL_WECHAT)
             .setStatus(VipOrder.STATUS_CREATED)
             .setCreateTime(now)
             .setUpdateTime(now);
        
        return order;
    }
    
    /**
     * 构建订单信息
     */
    private Map<String, Object> buildOrderInfo(VipOrder order) {
        Map<String, Object> info = new HashMap<>();
        info.put("orderNo", order.getOrderNo());
        info.put("plan", order.getPlan());
        info.put("planName", getPlanName(order.getPlan()));
        info.put("amount", order.getAmountYuan());
        info.put("status", order.getStatus());
        info.put("statusName", getStatusName(order.getStatus()));
        info.put("createTime", order.getCreateTime());
        info.put("paidTime", order.getPaidTime());
        info.put("channelTradeNo", order.getChannelTradeNo());
        
        return info;
    }
    
    /**
     * 生成支付参数
     */
    private Map<String, Object> generatePayParams(VipOrder order) {
        try {
            if (!WechatPayService.me.isAvailable()) {
                LogKit.warn("微信支付服务不可用，返回模拟参数");
                return createMockPayParams(order);
            }
            
            return WechatPayService.me.createAppOrder(order);
            
        } catch (Exception e) {
            LogKit.error("生成支付参数失败", e);
            return createMockPayParams(order);
        }
    }
    
    /**
     * 创建模拟支付参数（用于测试）
     */
    private Map<String, Object> createMockPayParams(VipOrder order) {
        Map<String, Object> params = new HashMap<>();
        params.put("appid", "wx_mock_appid");
        params.put("partnerid", "mock_mchid");
        params.put("prepayid", "wx_mock_prepay_" + System.currentTimeMillis());
        params.put("package", "Sign=WXPay");
        params.put("noncestr", "mock_nonce_" + System.currentTimeMillis());
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("sign", "mock_signature");
        params.put("_mock", true);
        
        return params;
    }
    
    /**
     * 查找用户未支付的订单
     */
    private VipOrder findUnpaidOrder(Integer userId) {
        return VipOrder.dao.findFirst(
            "SELECT * FROM vip_order WHERE user_id = ? AND status = ? ORDER BY create_time DESC LIMIT 1",
            userId, VipOrder.STATUS_CREATED
        );
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "VIP" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }
    
    /**
     * 验证套餐类型
     */
    private boolean isValidPlan(String plan) {
        return VipOrder.PLAN_MONTHLY.equals(plan) || VipOrder.PLAN_YEARLY.equals(plan);
    }
    
    /**
     * 套餐转用户等级
     */
    private UserTier planToTier(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return UserTier.VIP_MONTHLY;
            case VipOrder.PLAN_YEARLY:
                return UserTier.VIP_YEARLY;
            default:
                return UserTier.VIP_MONTHLY;
        }
    }
    
    /**
     * 获取套餐价格
     */
    private int getPlanPrice(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return MONTHLY_PRICE;
            case VipOrder.PLAN_YEARLY:
                return YEARLY_PRICE;
            default:
                return MONTHLY_PRICE;
        }
    }
    
    /**
     * 获取套餐名称
     */
    private String getPlanName(String plan) {
        switch (plan) {
            case VipOrder.PLAN_MONTHLY:
                return "VIP月度会员";
            case VipOrder.PLAN_YEARLY:
                return "VIP年度会员";
            default:
                return "VIP会员";
        }
    }
    
    /**
     * 获取状态名称
     */
    private String getStatusName(String status) {
        switch (status) {
            case VipOrder.STATUS_CREATED:
                return "待支付";
            case VipOrder.STATUS_PAID:
                return "已支付";
            case VipOrder.STATUS_CLOSED:
                return "已关闭";
            case VipOrder.STATUS_FAILED:
                return "支付失败";
            default:
                return "未知状态";
        }
    }
}
