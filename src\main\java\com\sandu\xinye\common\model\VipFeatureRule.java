package com.sandu.xinye.common.model;

import com.sandu.xinye.common.model.base.BaseVipFeatureRule;
import com.sandu.xinye.common.enums.PermissionType;

/**
 * VIP功能规则模型
 */
@SuppressWarnings("serial")
public class VipFeatureRule extends BaseVipFeatureRule<VipFeatureRule> {
	public static final VipFeatureRule dao = new VipFeatureRule().dao();

	/**
	 * 获取权限类型枚举
	 */
	public PermissionType getPermissionType() {
		String permission = getPermission();
		if (permission == null) {
			return null;
		}
		
		// 根据权限代码匹配枚举
		for (PermissionType type : PermissionType.values()) {
			if (type.getCode().equals(permission)) {
				return type;
			}
		}
		return null;
	}

	/**
	 * 判断规则是否启用
	 */
	public boolean isEnabled() {
		Boolean enabled = getEnabled();
		return enabled != null && enabled;
	}

	/**
	 * 判断是否匹配指定的HTTP方法和路径
	 */
	public boolean matches(String httpMethod, String requestPath) {
		if (!isEnabled()) {
			return false;
		}
		
		// 检查HTTP方法
		String ruleMethod = getMethod();
		if (ruleMethod != null && !ruleMethod.equalsIgnoreCase(httpMethod)) {
			return false;
		}
		
		// 检查路径模式
		String pattern = getPattern();
		if (pattern == null) {
			return false;
		}
		
		// 简单前缀匹配（后续可扩展为正则或通配符）
		return requestPath.startsWith(pattern);
	}
}
