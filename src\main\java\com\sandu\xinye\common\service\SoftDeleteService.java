package com.sandu.xinye.common.service;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Templet;
import com.sandu.xinye.common.model.TempletGroup;
import com.sandu.xinye.common.model.Cloudfile;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 软删除服务
 * 统一管理所有支持软删除的数据类型
 * 
 * <AUTHOR> Team
 * @since 2024-07-18
 */
public class SoftDeleteService {
    
    public static final SoftDeleteService me = new SoftDeleteService();
    
    // 支持的数据类型常量
    public static final String DATA_TYPE_TEMPLATE = "template";
    public static final String DATA_TYPE_TEMPLATE_GROUP = "template_group";
    public static final String DATA_TYPE_CLOUDFILE = "cloudfile";
    
    /**
     * 软删除模板
     * @param templateId 模板ID
     * @param userId 用户ID（用于权限验证）
     * @return 删除结果
     */
    public RetKit softDeleteTemplate(Integer templateId, Integer userId) {
        if (templateId == null || userId == null) {
            return RetKit.fail("参数不能为空");
        }

        Templet template = Templet.dao.findById(templateId);
        if (template == null) {
            return RetKit.fail("模板不存在");
        }

        // 权限验证：只能删除自己的模板
        if (!template.getUserId().equals(userId)) {
            return RetKit.fail("无权限删除此模板");
        }

        // 检查是否已经被删除
        if (template.get("deleteTime") != null) {
            return RetKit.fail("模板已被删除");
        }

        // 使用事务确保模板软删除和历史记录删除的原子性
        return Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    // 1. 执行模板软删除
                    template.set("deleteTime", new Date());
                    boolean templateSuccess = template.update();

                    if (!templateSuccess) {
                        LogKit.error("模板软删除失败: templateId=" + templateId);
                        return false;
                    }

                    // 2. 删除对应的历史记录
                    int deletedHistoryCount = Db.update(
                        "DELETE FROM templet_history WHERE templetId = ?",
                        templateId
                    );

                    LogKit.info("模板软删除成功: templateId=" + templateId + ", userId=" + userId +
                               ", 删除历史记录数量=" + deletedHistoryCount);
                    return true;

                } catch (Exception e) {
                    LogKit.error("模板软删除事务异常: " + e.getMessage(), e);
                    return false;
                }
            }
        }) ? RetKit.ok("模板删除成功") : RetKit.fail("删除失败");
    }
    
    /**
     * 软删除模板分组
     * @param groupId 分组ID
     * @param userId 用户ID（用于权限验证）
     * @return 删除结果
     */
    public RetKit softDeleteTemplateGroup(Integer groupId, Integer userId) {
        if (groupId == null || userId == null) {
            return RetKit.fail("参数不能为空");
        }

        TempletGroup group = TempletGroup.dao.findById(groupId);
        if (group == null) {
            return RetKit.fail("分组不存在");
        }

        // 权限验证：只能删除自己的分组
        if (!group.getUserId().equals(userId)) {
            return RetKit.fail("无权限删除此分组");
        }

        // 检查是否已经被删除
        if (group.get("deleteTime") != null) {
            return RetKit.fail("分组已被删除");
        }

        // 使用事务确保分组软删除和历史记录删除的原子性
        return Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    // 1. 执行分组软删除
                    group.set("deleteTime", new Date());
                    boolean groupSuccess = group.update();

                    if (!groupSuccess) {
                        LogKit.error("模板分组软删除失败: groupId=" + groupId);
                        return false;
                    }

                    // 2. 删除分组内所有模板对应的历史记录
                    // 首先查询分组内的所有模板ID
                    List<Record> templateRecords = Db.find(
                        "SELECT id FROM templet WHERE groupId = ? AND userId = ? AND deleteTime IS NULL",
                        groupId, userId
                    );

                    int totalDeletedHistoryCount = 0;
                    if (!templateRecords.isEmpty()) {
                        // 构建模板ID列表用于批量删除历史记录
                        List<Integer> templateIds = new ArrayList<>();
                        for (Record record : templateRecords) {
                            templateIds.add(record.getInt("id"));
                        }

                        // 批量删除历史记录
                        if (!templateIds.isEmpty()) {
                            String inClause = String.join(",", Collections.nCopies(templateIds.size(), "?"));
                            String deleteSql = "DELETE FROM templet_history WHERE templetId IN (" + inClause + ")";
                            totalDeletedHistoryCount = Db.update(deleteSql, templateIds.toArray());
                        }
                    }

                    LogKit.info("模板分组软删除成功: groupId=" + groupId + ", userId=" + userId +
                               ", 分组内模板数量=" + templateRecords.size() +
                               ", 删除历史记录数量=" + totalDeletedHistoryCount);
                    return true;

                } catch (Exception e) {
                    LogKit.error("模板分组软删除事务异常: " + e.getMessage(), e);
                    return false;
                }
            }
        }) ? RetKit.ok("分组删除成功") : RetKit.fail("删除失败");
    }
    
    /**
     * 软删除云端文件
     * @param fileId 文件ID
     * @param userId 用户ID（用于权限验证）
     * @return 删除结果
     */
    public RetKit softDeleteCloudfile(Integer fileId, Integer userId) {
        if (fileId == null || userId == null) {
            return RetKit.fail("参数不能为空");
        }
        
        Cloudfile file = Cloudfile.dao.findById(fileId);
        if (file == null) {
            return RetKit.fail("文件不存在");
        }
        
        // 权限验证：只能删除自己的文件
        if (!file.getUserId().equals(userId)) {
            return RetKit.fail("无权限删除此文件");
        }
        
        // 检查是否已经被删除
        if (file.get("deleteTime") != null) {
            return RetKit.fail("文件已被删除");
        }
        
        try {
            // 执行软删除
            file.set("deleteTime", new Date());
            boolean success = file.update();
            
            if (success) {
                LogKit.info("云端文件软删除成功: fileId=" + fileId + ", userId=" + userId);
                return RetKit.ok("文件删除成功");
            } else {
                return RetKit.fail("删除失败");
            }
        } catch (Exception e) {
            LogKit.error("云端文件软删除异常: " + e.getMessage(), e);
            return RetKit.fail("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量软删除模板
     * @param templateIds 模板ID列表
     * @param userId 用户ID
     * @return 删除结果
     */
    public RetKit batchSoftDeleteTemplates(List<Integer> templateIds, Integer userId) {
        if (templateIds == null || templateIds.isEmpty() || userId == null) {
            return RetKit.fail("参数不能为空");
        }
        
        return Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                int successCount = 0;
                int failCount = 0;
                
                for (Integer templateId : templateIds) {
                    RetKit result = softDeleteTemplate(templateId, userId);
                    if (result.success()) {
                        successCount++;
                    } else {
                        failCount++;
                        LogKit.warn("批量删除模板失败: templateId=" + templateId + ", error=" + result.getMsg());
                    }
                }
                
                LogKit.info("批量软删除模板完成: 成功=" + successCount + ", 失败=" + failCount);
                return failCount == 0; // 只有全部成功才提交事务
            }
        }) ? RetKit.ok("批量删除成功") : RetKit.fail("批量删除失败");
    }
    
    /**
     * 检查数据是否已被软删除
     * @param dataType 数据类型
     * @param dataId 数据ID
     * @return true表示已删除，false表示未删除
     */
    public boolean isDeleted(String dataType, Integer dataId) {
        if (dataType == null || dataId == null) {
            return false;
        }
        
        String tableName = getTableName(dataType);
        if (tableName == null) {
            return false;
        }
        
        try {
            Object deleteTime = Db.queryColumn(
                "SELECT deleteTime FROM " + tableName + " WHERE id = ?", 
                dataId
            );
            return deleteTime != null;
        } catch (Exception e) {
            LogKit.error("检查删除状态异常: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 根据数据类型获取表名
     * @param dataType 数据类型
     * @return 表名
     */
    private String getTableName(String dataType) {
        switch (dataType) {
            case DATA_TYPE_TEMPLATE:
                return "templet";
            case DATA_TYPE_TEMPLATE_GROUP:
                return "templet_group";
            case DATA_TYPE_CLOUDFILE:
                return "cloudfile";
            default:
                return null;
        }
    }
}
