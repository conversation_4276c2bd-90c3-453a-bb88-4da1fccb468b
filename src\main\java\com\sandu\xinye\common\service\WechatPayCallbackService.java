package com.sandu.xinye.common.service;

import com.jfinal.kit.JsonKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.common.kit.WechatPaySignKit;
import com.sandu.xinye.common.model.VipOrder;
import com.sandu.xinye.common.model.VipPayNotifyLog;

import javax.servlet.http.HttpServletRequest;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.Map;

/**
 * 微信支付回调处理服务
 * 处理微信支付的异步通知
 */
public class WechatPayCallbackService {
    
    public static final WechatPayCallbackService me = new WechatPayCallbackService();
    
    /**
     * 处理微信支付回调
     */
    public boolean handleCallback(HttpServletRequest request, String requestBody) {
        String verifyResult = VipPayNotifyLog.VERIFY_FAILED;
        String handleStatus = VipPayNotifyLog.HANDLE_ERROR;
        String errorMsg = null;

        try {
            LogKit.info("收到微信支付回调");

            // 验证签名
            if (!verifySignature(request, requestBody)) {
                LogKit.error("微信支付回调签名验证失败");
                errorMsg = "签名验证失败";
                logCallback(request, requestBody, verifyResult, handleStatus, errorMsg);
                return false;
            }

            verifyResult = VipPayNotifyLog.VERIFY_SUCCESS;
            
            // 解析回调数据
            Map<String, Object> callbackData = JsonKit.parse(requestBody, Map.class);
            
            // 解密resource数据
            Map<String, Object> resource = (Map<String, Object>) callbackData.get("resource");
            String decryptedData = decryptResource(resource);
            
            if (StrKit.isBlank(decryptedData)) {
                LogKit.error("微信支付回调数据解密失败");
                return false;
            }
            
            // 解析支付结果
            Map<String, Object> paymentResult = JsonKit.parse(decryptedData, Map.class);
            
            // 处理支付成功
            return processPaymentSuccess(paymentResult);
            
        } catch (Exception e) {
            LogKit.error("处理微信支付回调失败", e);
            return false;
        }
    }
    
    /**
     * 验证微信支付回调签名
     */
    private boolean verifySignature(HttpServletRequest request, String requestBody) {
        try {
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String signature = request.getHeader("Wechatpay-Signature");
            String serialNo = request.getHeader("Wechatpay-Serial");
            
            if (StrKit.isBlank(timestamp) || StrKit.isBlank(nonce) || 
                StrKit.isBlank(signature) || StrKit.isBlank(serialNo)) {
                LogKit.error("微信支付回调缺少必要的签名头部");
                return false;
            }
            
            // TODO: 获取微信支付平台证书
            // 实际应用中需要从微信支付平台下载证书并缓存
            X509Certificate certificate = getWechatPayCertificate(serialNo);
            if (certificate == null) {
                LogKit.error("无法获取微信支付平台证书: " + serialNo);
                return false;
            }
            
            return WechatPaySignKit.verifySignature(timestamp, nonce, requestBody, signature, certificate);
            
        } catch (Exception e) {
            LogKit.error("验证微信支付回调签名失败", e);
            return false;
        }
    }
    
    /**
     * 解密resource数据
     */
    private String decryptResource(Map<String, Object> resource) {
        try {
            String algorithm = (String) resource.get("algorithm");
            String ciphertext = (String) resource.get("ciphertext");
            String associatedData = (String) resource.get("associated_data");
            String nonce = (String) resource.get("nonce");
            
            if (!"AEAD_AES_256_GCM".equals(algorithm)) {
                LogKit.error("不支持的加密算法: " + algorithm);
                return null;
            }
            
            String apiV3Key = WechatPayConfig.me.getApiV3Key();
            return WechatPaySignKit.decryptCallbackData(associatedData, nonce, ciphertext, apiV3Key);
            
        } catch (Exception e) {
            LogKit.error("解密微信支付回调数据失败", e);
            return null;
        }
    }
    
    /**
     * 处理支付成功
     */
    private boolean processPaymentSuccess(Map<String, Object> paymentResult) {
        try {
            String tradeState = (String) paymentResult.get("trade_state");
            String outTradeNo = (String) paymentResult.get("out_trade_no");
            String transactionId = (String) paymentResult.get("transaction_id");
            String successTime = (String) paymentResult.get("success_time");
            
            LogKit.info("微信支付结果: " + tradeState + ", 订单号: " + outTradeNo + ", 微信订单号: " + transactionId);
            
            // 只处理支付成功的订单
            if (!"SUCCESS".equals(tradeState)) {
                LogKit.info("订单支付状态非成功，跳过处理: " + tradeState);
                return true; // 返回true表示回调处理成功，避免微信重复推送
            }
            
            // 查找订单
            VipOrder order = VipOrder.dao.findFirst("SELECT * FROM vip_order WHERE order_no = ?", outTradeNo);
            if (order == null) {
                LogKit.error("未找到订单: " + outTradeNo);
                return false;
            }
            
            // 检查订单状态，避免重复处理
            if (VipOrder.STATUS_PAID.equals(order.getStatus())) {
                LogKit.info("订单已处理过，跳过: " + outTradeNo);
                return true;
            }
            
            // 更新订单状态
            Date now = new Date();
            order.setStatus(VipOrder.STATUS_PAID)
                 .setChannelTradeNo(transactionId)
                 .setPaidTime(parseSuccessTime(successTime))
                 .setNotifyTime(now)
                 .setUpdateTime(now);
            
            boolean updateSuccess = order.update();
            if (!updateSuccess) {
                LogKit.error("更新订单状态失败: " + outTradeNo);
                return false;
            }
            
            LogKit.info("订单状态更新成功: " + outTradeNo);
            
            // 触发VIP开通/续期
            return VipDeliveryService.me.deliverVip(order);
            
        } catch (Exception e) {
            LogKit.error("处理支付成功失败", e);
            return false;
        }
    }
    
    /**
     * 解析微信支付成功时间
     */
    private Date parseSuccessTime(String successTime) {
        try {
            if (StrKit.isBlank(successTime)) {
                return new Date();
            }
            // 微信支付时间格式: 2018-06-08T10:34:56+08:00
            // 简化处理，实际应用中需要正确解析ISO 8601格式
            return new Date();
        } catch (Exception e) {
            LogKit.warn("解析支付成功时间失败: " + successTime, e);
            return new Date();
        }
    }
    
    /**
     * 获取微信支付平台证书
     * TODO: 实际应用中需要从微信支付平台下载证书并缓存
     */
    private X509Certificate getWechatPayCertificate(String serialNo) {
        // 临时返回null，实际需要实现证书下载和缓存
        LogKit.warn("微信支付平台证书获取未实现，序列号: " + serialNo);
        return null;
    }

    /**
     * 记录回调日志
     */
    private void logCallback(HttpServletRequest request, String requestBody, String verifyResult, String handleStatus, String errorMsg) {
        try {
            VipPayNotifyLog log = new VipPayNotifyLog();
            log.setChannel(VipPayNotifyLog.CHANNEL_WECHAT)
               .setNotifyTime(new Date())
               .setRawHeaders(buildHeadersString(request))
               .setRawBody(requestBody)
               .setVerifyResult(verifyResult)
               .setHandleStatus(handleStatus)
               .setErrorMsg(errorMsg)
               .setCreateTime(new Date());

            log.save();

        } catch (Exception e) {
            LogKit.error("记录支付回调日志失败", e);
        }
    }

    /**
     * 构建请求头字符串
     */
    private String buildHeadersString(HttpServletRequest request) {
        StringBuilder headers = new StringBuilder();
        headers.append("Wechatpay-Timestamp: ").append(request.getHeader("Wechatpay-Timestamp")).append("\n");
        headers.append("Wechatpay-Nonce: ").append(request.getHeader("Wechatpay-Nonce")).append("\n");
        headers.append("Wechatpay-Signature: ").append(request.getHeader("Wechatpay-Signature")).append("\n");
        headers.append("Wechatpay-Serial: ").append(request.getHeader("Wechatpay-Serial")).append("\n");
        headers.append("Content-Type: ").append(request.getContentType());
        return headers.toString();
    }
}
