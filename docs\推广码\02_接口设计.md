# 推广码功能接口设计

## 1. 接口概述

推广码功能需要修改现有的注册接口，并新增推广码绑定接口。所有接口遵循现有的API设计规范，保持与系统的一致性。

## 2. 注册接口改造

### 2.1 手机注册接口

**接口路径**: `POST /api/register`  
**控制器**: `UserLoginController.register()`  
**服务类**: `UserLoginService.register()`

#### 2.1.1 请求参数
```json
{
    "phone": "13800138000",           // 必填，手机号
    "password": "123456",             // 必填，密码
    "captcha": "1234",                // 必填，验证码
    "promotionCode": "ABC123"         // 新增，可选，推广码
}
```

#### 2.1.2 响应格式
```json
{
    "state": true,
    "msg": "注册成功",
    "data": {
        "userId": 12345,
        "promotionCode": "ABC123",    // 绑定的推广码（如果有）
        "promotionBound": true        // 推广码绑定状态
    }
}

// 未绑定推广码的响应
{
    "state": true,
    "msg": "注册成功",
    "data": {
        "userId": 12345,
        "promotionCode": null,        // 未绑定推广码
        "promotionBound": false       // 推广码绑定状态
    }
}
```

#### 2.1.3 业务逻辑
1. 验证手机号、密码、验证码（现有逻辑）
2. 如果传入了promotionCode，验证推广码格式
3. 创建用户记录，同时绑定推广码
4. 返回注册结果，包含推广码绑定状态

### 2.2 验证码登录接口

**接口路径**: `POST /api/v2/captchaLogin`  
**控制器**: `UserLoginController.captchaLogin()`  
**服务类**: `UserLoginService.captchaLogin()`

#### 2.2.1 请求参数
```json
{
    "phone": "13800138000",           // 必填，手机号
    "captcha": "1234",                // 必填，验证码
    "promotionCode": "ABC123"         // 新增，可选，推广码（仅新用户注册时有效）
}
```

#### 2.2.2 业务逻辑
- 如果是新用户注册，支持推广码绑定
- 如果是已有用户登录，忽略推广码参数

### 2.3 第三方登录接口

**接口路径**: `POST /api/v2/fasterLogin`  
**控制器**: `UserLoginController.fasterLogin()`  
**服务类**: `UserLoginService.fasterLogin()`

#### 2.3.1 请求参数
```json
{
    "loginType": 1,                   // 必填，登录类型
    "openId": "wx_open_id",           // 必填，第三方openId
    "nickName": "用户昵称",            // 可选，昵称
    "promotionCode": "ABC123"         // 新增，可选，推广码（仅新用户注册时有效）
}
```

### 2.4 苹果登录接口

**接口路径**: `POST /api/v2/appleLogin`  
**控制器**: `UserLoginController.appleLogin()`  
**服务类**: `UserLoginService.appleLogin()`

#### 2.4.1 请求参数
```json
{
    "appleUserId": "apple_user_id",   // 必填，苹果用户ID
    "promotionCode": "ABC123"         // 新增，可选，推广码（仅新用户注册时有效）
}
```

#### 2.4.2 业务逻辑
- 如果是新用户注册，支持推广码绑定
- 如果是已有用户登录，忽略推广码参数

## 3. 推广码绑定接口

### 3.1 绑定推广码

**接口路径**: `POST /api/v2/user/bindPromotionCode`
**控制器**: `UserController.bindPromotionCode()` (新增)
**服务类**: `UserService.bindPromotionCode()` (新增)

#### 3.1.1 请求参数
```json
{
    "promotionCode": "ABC123"         // 必填，推广码
}
```

#### 3.1.2 请求头
```
Authorization: Bearer {accessToken}  // 必填，用户登录token
```

#### 3.1.3 响应格式
```json
// 成功响应
{
    "state": true,
    "msg": "推广码绑定成功",
    "data": {
        "promotionCode": "ABC123",
        "bindTime": "2024-07-30 10:30:00"
    }
}

// 失败响应
{
    "state": false,
    "msg": "推广码格式错误",
    "data": null
}
```

#### 3.1.4 错误码定义
| 错误信息 | 说明 |
|----------|------|
| "推广码不能为空" | promotionCode参数为空 |
| "推广码格式错误" | 推广码格式不符合规范 |
| "您已绑定推广码，不能重复绑定" | 用户已经绑定过推广码 |
| "推广码无效" | 推广码不存在或已失效 |
| "绑定失败，请稍后重试" | 数据库操作失败 |

#### 3.1.5 业务逻辑
1. 验证用户登录状态
2. 检查用户是否已绑定推广码
3. 验证推广码格式
4. 可选：调用第三方接口验证推广码有效性
5. 更新用户表，绑定推广码和时间
6. 返回绑定结果

### 3.2 查询推广码绑定状态

**接口路径**: `GET /api/v2/user/promotionCodeStatus`
**控制器**: `UserController.getPromotionCodeStatus()` (新增)

#### 3.2.1 请求头
```
Authorization: Bearer {accessToken}  // 必填，用户登录token
```

#### 3.2.2 响应格式
```json
{
    "state": true,
    "msg": "查询成功",
    "data": {
        "hasBound": true,                    // 是否已绑定
        "promotionCode": "ABC123",           // 绑定的推广码（如果已绑定）
        "bindTime": "2024-07-30 10:30:00"   // 绑定时间（如果已绑定）
    }
}
```

## 4. 推广码验证规则

### 4.1 格式验证
```java
public class PromotionCodeValidator {
    
    // 推广码格式：6-8位字母数字组合
    private static final String PROMOTION_CODE_PATTERN = "^[A-Za-z0-9]{6,8}$";
    
    public static boolean isValidFormat(String promotionCode) {
        if (StrKit.isBlank(promotionCode)) {
            return false;
        }
        return promotionCode.matches(PROMOTION_CODE_PATTERN);
    }
    
    // 标准化推广码（转为大写）
    public static String normalize(String promotionCode) {
        return promotionCode.toUpperCase().trim();
    }
}
```

### 4.2 业务验证
- 推广码格式验证（必须）
- 推广码有效性验证（可选，调用第三方接口）
- 重复绑定检查（必须）
- 用户权限验证（必须）

## 5. 数据库操作

### 5.1 推广码绑定SQL
```sql
-- 检查用户是否已绑定推广码
SELECT promotion_code FROM user WHERE userId = ?;

-- 绑定推广码
UPDATE user 
SET promotion_code = ?, promotion_bind_time = NOW() 
WHERE userId = ? AND promotion_code IS NULL;

-- 查询用户推广码信息
SELECT promotion_code, promotion_bind_time 
FROM user 
WHERE userId = ?;
```

### 5.2 统计查询SQL
```sql
-- 查询推广码绑定用户数
SELECT COUNT(*) FROM user WHERE promotion_code = ?;

-- 查询用户的推广码绑定历史
SELECT userId, userNickName, promotion_code, promotion_bind_time 
FROM user 
WHERE promotion_code IS NOT NULL 
ORDER BY promotion_bind_time DESC;
```

## 6. 错误处理

### 6.1 参数验证错误
```java
// 推广码为空
if (StrKit.isBlank(promotionCode)) {
    return RetKit.fail("推广码不能为空");
}

// 推广码格式错误
if (!PromotionCodeValidator.isValidFormat(promotionCode)) {
    return RetKit.fail("推广码格式错误");
}
```

### 6.2 业务逻辑错误
```java
// 检查重复绑定
User user = User.dao.findById(userId);
if (StrKit.notBlank(user.getPromotionCode())) {
    return RetKit.fail("您已绑定推广码，不能重复绑定");
}

// 数据库操作失败
boolean success = user.setPromotionCode(promotionCode)
                     .setPromotionBindTime(new Date())
                     .update();
if (!success) {
    return RetKit.fail("绑定失败，请稍后重试");
}
```

## 7. 日志记录

### 7.1 操作日志
```java
// 推广码绑定成功日志
LogKit.info("用户绑定推广码成功 - userId: " + userId + ", promotionCode: " + promotionCode);

// 推广码绑定失败日志
LogKit.warn("用户绑定推广码失败 - userId: " + userId + ", promotionCode: " + promotionCode + ", reason: " + reason);

// 重复绑定尝试日志
LogKit.warn("用户尝试重复绑定推广码 - userId: " + userId + ", existingCode: " + existingCode + ", newCode: " + promotionCode);
```

### 7.2 统计日志
```java
// 每日推广码绑定统计
LogKit.info("每日推广码绑定统计 - date: " + date + ", count: " + count);
```

## 8. 安全考虑

### 8.1 接口安全
- 推广码绑定接口需要用户登录
- 使用现有的token验证机制
- 防止恶意绑定和重复绑定

### 8.2 数据安全
- 推广码信息不包含敏感数据
- 绑定操作使用事务确保数据一致性
- 记录操作日志便于审计

## 9. 测试用例

### 9.1 注册接口测试
```java
// 测试用例1：正常注册并绑定推广码
@Test
public void testRegisterWithPromotionCode() {
    // 准备测试数据
    // 调用注册接口
    // 验证用户创建成功
    // 验证推广码绑定成功
}

// 测试用例2：注册时推广码格式错误
@Test
public void testRegisterWithInvalidPromotionCode() {
    // 准备无效推广码
    // 调用注册接口
    // 验证返回格式错误信息
}
```

### 9.2 绑定接口测试
```java
// 测试用例1：正常绑定推广码
@Test
public void testBindPromotionCode() {
    // 创建测试用户
    // 调用绑定接口
    // 验证绑定成功
}

// 测试用例2：重复绑定推广码
@Test
public void testDuplicateBindPromotionCode() {
    // 创建已绑定推广码的用户
    // 尝试再次绑定
    // 验证返回重复绑定错误
}
```
