package com.sandu.xinye.common.service;

import com.jfinal.kit.LogKit;
import com.jfinal.plugin.ehcache.CacheKit;
import com.sandu.xinye.common.model.VipFeatureRule;
import com.sandu.xinye.common.enums.PermissionType;

import java.util.List;

/**
 * VIP功能规则服务
 * 提供基于数据库配置的路由匹配和权限检查
 */
public class VipFeatureRuleService {
    
    public static final VipFeatureRuleService me = new VipFeatureRuleService();
    
    private static final String CACHE_NAME = "vip_feature_rules";
    private static final String CACHE_KEY_ALL_RULES = "all_rules";
    
    /**
     * 匹配HTTP请求的VIP功能规则
     */
    public VipFeatureRule match(String httpMethod, String requestPath) {
        if (httpMethod == null || requestPath == null) {
            return null;
        }
        
        try {
            List<VipFeatureRule> rules = getAllRules();
            
            for (VipFeatureRule rule : rules) {
                if (rule.matches(httpMethod, requestPath)) {
                    LogKit.debug("VIP规则匹配: " + httpMethod + " " + requestPath + " -> " + rule.getPermission());
                    return rule;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LogKit.error("VIP规则匹配失败: " + httpMethod + " " + requestPath, e);
            return null;
        }
    }
    
    /**
     * 获取所有启用的规则（带缓存）
     */
    @SuppressWarnings("unchecked")
    private List<VipFeatureRule> getAllRules() {
        List<VipFeatureRule> rules = CacheKit.get(CACHE_NAME, CACHE_KEY_ALL_RULES);
        
        if (rules == null) {
            rules = VipFeatureRule.dao.find(
                "SELECT * FROM vip_feature_rule WHERE enabled = 1 ORDER BY id"
            );
            
            // 缓存5分钟
            CacheKit.put(CACHE_NAME, CACHE_KEY_ALL_RULES, rules, 300);
            
            LogKit.info("加载VIP功能规则: " + rules.size() + " 条");
        }
        
        return rules;
    }
    
    /**
     * 清除规则缓存
     */
    public void clearCache() {
        CacheKit.remove(CACHE_NAME, CACHE_KEY_ALL_RULES);
        LogKit.info("清除VIP功能规则缓存");
    }
    
    /**
     * 添加新规则
     */
    public boolean addRule(String method, String pattern, PermissionType permissionType, boolean enabled) {
        try {
            VipFeatureRule rule = new VipFeatureRule();
            rule.setMethod(method.toUpperCase())
                .setPattern(pattern)
                .setPermission(permissionType.getCode())
                .setEnabled(enabled)
                .setCreateTime(new java.util.Date())
                .setUpdateTime(new java.util.Date());
            
            boolean success = rule.save();
            if (success) {
                clearCache(); // 清除缓存以便重新加载
                LogKit.info("添加VIP功能规则: " + method + " " + pattern + " -> " + permissionType.getCode());
            }
            
            return success;
            
        } catch (Exception e) {
            LogKit.error("添加VIP功能规则失败", e);
            return false;
        }
    }
    
    /**
     * 初始化默认规则（OCR识图）
     */
    public void initDefaultRules() {
        try {
            // 检查是否已有OCR规则
            VipFeatureRule existingRule = VipFeatureRule.dao.findFirst(
                "SELECT * FROM vip_feature_rule WHERE method = ? AND pattern = ?",
                "POST", "/api/v2/ocr/recognize"
            );
            
            if (existingRule == null) {
                addRule("POST", "/api/v2/ocr/recognize", PermissionType.FEATURE_OCR_CREATE, true);
                LogKit.info("初始化OCR识图VIP规则");
            }
            
        } catch (Exception e) {
            LogKit.error("初始化默认VIP规则失败", e);
        }
    }
}
