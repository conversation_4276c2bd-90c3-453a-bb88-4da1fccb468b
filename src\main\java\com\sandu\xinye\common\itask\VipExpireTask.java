package com.sandu.xinye.common.itask;

import com.jfinal.kit.LogKit;
import com.sandu.xinye.common.service.VipDeliveryService;

/**
 * VIP过期处理定时任务
 * 定期检查并处理过期的VIP用户
 */
public class VipExpireTask {
    
    /**
     * 处理过期VIP用户
     * 建议每天执行一次
     */
    public static void processExpiredVipUsers() {
        try {
            LogKit.info("开始执行VIP过期处理任务");
            
            VipDeliveryService.me.processExpiredVipUsers();
            
            LogKit.info("VIP过期处理任务执行完成");
            
        } catch (Exception e) {
            LogKit.error("VIP过期处理任务执行失败", e);
        }
    }
}
