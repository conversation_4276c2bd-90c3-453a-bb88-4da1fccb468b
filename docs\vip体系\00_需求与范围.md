# VIP体系需求与范围（v1）

## 背景
- 支付能力：支持用户通过微信支付购买 VIP
- 套餐与价格：月度 9.9 元，年度 99 元（一次性购买，不含自动续费）
- 功能控制：识图新建（OCR）为 VIP 专享功能

## 目标
- 支持创建 VIP 订单并通过微信支付完成交易
- 订单成功后即时为用户开通 VIP（更新用户等级与到期时间）
- 在后端对 OCR 接口进行权限拦截，非 VIP 拒绝访问并返回升级提示

## 关键术语
- VIP 等级（UserTier）：FREE、VIP_MONTHLY、VIP_YEARLY（已存在 FREE/VIP_MONTHLY/VIP_YEARLY 的枚举）
- VIP 到期时间（vipExpireTime）：新增（建议）用于区分 VIP 有效期
- 支付渠道：WECHAT（微信支付）
- 订单：VIP 订单，包含用户、套餐、金额、支付渠道和状态

## 用户故事
- 作为用户，我可以选择购买月度或年度 VIP，并使用微信完成支付
- 作为用户，支付成功后我能立即享有 VIP 权限，能够使用识图新建功能
- 作为用户，我可以随时查看我的 VIP 状态与到期时间

## 范围
- 范围内（v1）
  - 微信支付下单、支付回调与订单状态更新
  - 用户 VIP 等级与到期时间的计算与写入
  - OCR 接口的 VIP 权限校验（PermissionType.FEATURE_OCR_CREATE）
  - 基础查询接口：获取当前用户 VIP 状态
  - 后台配置与日志、幂等处理
- 范围外（后续版本）
  - 自动续费/订阅扣费
  - 多支付渠道（支付宝、苹果内购等）
  - 发票、优惠券、活动价格、拼团等营销玩法
  - 完整的订阅生命周期管理（变更/退款/申诉/风控等）

## 非功能需求
- 安全：微信支付 V3 签名校验、回调验签、数据加密
- 可观测：关键链路日志，订单与回调的审计记录
- 幂等：回调与发货（开通 VIP）必须幂等
- 兼容性：不影响现有登录、数据恢复等功能
- 性能：订单创建/状态查询为轻量接口，限制 QPS 保护

## 支付场景
- App 原生支付：采用微信支付 APP 场景（trade_type=APP），所有购买在 APP 内完成

## 待确认与风险
- 商户配置：商户号、API v3 密钥、证书与 appid 正在申请中；代码中预留配置与证书路径，便于随时调整
- VIP 失效策略：到期降级为 FREE；同等级叠加有效期
- 退款策略：v1 不支持退款（或手工处理），正式上线前需制定策略

