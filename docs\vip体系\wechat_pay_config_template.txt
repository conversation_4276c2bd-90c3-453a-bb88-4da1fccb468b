# 微信支付配置模板
# 请将以下配置添加到 common_config.txt 中

# 微信支付应用ID（从微信开放平台获取）
wechat.pay.appid=wx1234567890abcdef

# 微信支付商户号（从微信支付商户平台获取）
wechat.pay.mchid=1234567890

# 微信支付API v3密钥（从微信支付商户平台设置）
wechat.pay.api.v3.key=your_api_v3_key_32_characters_long

# 微信支付证书序列号（从微信支付商户平台获取）
wechat.pay.serial.no=1234567890ABCDEF1234567890ABCDEF12345678

# 微信支付私钥文件路径（相对于项目根目录或绝对路径）
wechat.pay.private.key.path=certs/wechat_pay_private_key.pem

# 微信支付回调通知地址（需要外网可访问）
wechat.pay.notify.url=https://your-domain.com/api/v2/vip/pay/wechat/notify

# 配置说明：
# 1. appid: 在微信开放平台注册应用后获得
# 2. mchid: 在微信支付商户平台获得
# 3. api.v3.key: 在微信支付商户平台的API安全中设置，32位字符串
# 4. serial.no: 在微信支付商户平台的API证书中查看
# 5. private.key.path: 从微信支付商户平台下载的私钥文件路径
# 6. notify.url: 支付成功后微信回调的地址，必须是HTTPS且外网可访问

# 证书文件说明：
# - 需要从微信支付商户平台下载API证书
# - 将私钥文件（apiclient_key.pem）放到指定路径
# - 确保应用有读取证书文件的权限

# 安全提醒：
# - API v3密钥和私钥文件包含敏感信息，请妥善保管
# - 不要将密钥提交到代码仓库
# - 生产环境建议使用环境变量或加密配置
